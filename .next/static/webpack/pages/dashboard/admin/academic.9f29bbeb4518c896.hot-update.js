"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard/admin/academic",{

/***/ "./pages/dashboard/admin/academic.js":
/*!*******************************************!*\
  !*** ./pages/dashboard/admin/academic.js ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminAcademic; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/DashboardLayout */ \"./components/DashboardLayout.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/ProtectedRoute */ \"./components/ProtectedRoute.js\");\n/* harmony import */ var _lib_academicService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../lib/academicService */ \"./lib/academicService.js\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../lib/authService */ \"./lib/authService.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Professional SVG Icons\nconst AcademicCapIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l9-5-9-5-9 5 9 5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 22,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined);\n};\n_c = AcademicCapIcon;\nconst PlusIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 28,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PlusIcon;\nconst EditIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EditIcon;\nconst TrashIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = TrashIcon;\nconst SaveIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3-3-3m3-3v12\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 46,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SaveIcon;\nfunction AdminAcademic() {\n    var _sessions_find, _gradingSystem_grades;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"classes\");\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gradingSystem, setGradingSystem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"percentage\",\n        passMarkPercentage: 40,\n        grades: [\n            {\n                grade: \"A\",\n                minScore: 80,\n                maxScore: 100,\n                points: 5,\n                remark: \"Excellent\"\n            },\n            {\n                grade: \"B\",\n                minScore: 70,\n                maxScore: 79,\n                points: 4,\n                remark: \"Very Good\"\n            },\n            {\n                grade: \"C\",\n                minScore: 60,\n                maxScore: 69,\n                points: 3,\n                remark: \"Good\"\n            },\n            {\n                grade: \"D\",\n                minScore: 50,\n                maxScore: 59,\n                points: 2,\n                remark: \"Pass\"\n            },\n            {\n                grade: \"E\",\n                minScore: 40,\n                maxScore: 49,\n                points: 1,\n                remark: \"Poor\"\n            },\n            {\n                grade: \"F\",\n                minScore: 0,\n                maxScore: 39,\n                points: 0,\n                remark: \"Fail\"\n            }\n        ]\n    });\n    const [showGradeModal, setShowGradeModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newGrade, setNewGrade] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editingGradeIndex, setEditingGradeIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalType, setModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherSearch, setTeacherSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedTeacher, setSelectedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newItem, setNewItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [filteredTeachers, setFilteredTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTeacherDropdown, setShowTeacherDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAcademicData();\n    }, []);\n    const loadAcademicData = async ()=>{\n        try {\n            console.log(\"Loading academic data...\");\n            const [classesData, subjectsData, sessionsData, teachersData] = await Promise.all([\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllClasses)(),\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllSubjects)(),\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllSessions)(),\n                (0,_lib_authService__WEBPACK_IMPORTED_MODULE_6__.getUsersByRole)(\"teacher\")\n            ]);\n            console.log(\"Teachers loaded:\", teachersData.length, teachersData);\n            setClasses(classesData);\n            setSubjects(subjectsData);\n            setSessions(sessionsData);\n            setTeachers(teachersData);\n            setFilteredTeachers(teachersData);\n        // Keep existing grading system\n        } catch (error) {\n            console.error(\"Error loading academic data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            // Mock API call - replace with actual implementation\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            alert(\"Academic setup saved successfully!\");\n        } catch (error) {\n            console.error(\"Error saving academic setup:\", error);\n            alert(\"Error saving academic setup. Please try again.\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleDelete = async (type, id)=>{\n        if (confirm(\"Are you sure you want to delete this \".concat(type, \"?\"))) {\n            try {\n                switch(type){\n                    case \"class\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteClass)(id);\n                        setClasses(classes.filter((c)=>c.id !== id));\n                        break;\n                    case \"subject\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteSubject)(id);\n                        setSubjects(subjects.filter((s)=>s.id !== id));\n                        break;\n                    case \"session\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteSession)(id);\n                        setSessions(sessions.filter((s)=>s.id !== id));\n                        break;\n                }\n                alert(\"\".concat(type, \" deleted successfully!\"));\n            } catch (error) {\n                console.error(\"Error deleting \".concat(type, \":\"), error);\n                alert(\"Error deleting \".concat(type, \". Please try again.\"));\n            }\n        }\n    };\n    const openAddModal = (type)=>{\n        setModalType(type);\n        setNewItem({});\n        setSelectedTeacher(null);\n        setTeacherSearch(\"\");\n        setFilteredTeachers([]);\n        setShowTeacherDropdown(false);\n        setShowAddModal(true);\n    };\n    const handleAddItem = async ()=>{\n        try {\n            setIsSaving(true);\n            let result;\n            switch(modalType){\n                case \"class\":\n                    const classData = {\n                        ...newItem,\n                        classTeacherId: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.uid,\n                        classTeacherName: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.name,\n                        classTeacherUsername: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.username,\n                        currentStudents: 0,\n                        capacity: 0 // Will be calculated based on enrolled students\n                    };\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createClass)(classData);\n                    setClasses([\n                        ...classes,\n                        result\n                    ]);\n                    break;\n                case \"subject\":\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createSubject)(newItem);\n                    setSubjects([\n                        ...subjects,\n                        result\n                    ]);\n                    break;\n                case \"session\":\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createSession)(newItem);\n                    setSessions([\n                        ...sessions,\n                        result\n                    ]);\n                    break;\n            }\n            setShowAddModal(false);\n            alert(\"\".concat(modalType, \" created successfully!\"));\n        } catch (error) {\n            console.error(\"Error creating \".concat(modalType, \":\"), error);\n            alert(\"Error creating \".concat(modalType, \". Please try again.\"));\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleTeacherSearch = (searchTerm)=>{\n        console.log(\"Searching for:\", searchTerm, \"in teachers:\", teachers);\n        setTeacherSearch(searchTerm);\n        setShowTeacherDropdown(true);\n        if (searchTerm.length > 0) {\n            const filtered = teachers.filter((teacher)=>{\n                var _teacher_name, _teacher_username;\n                return ((_teacher_name = teacher.name) === null || _teacher_name === void 0 ? void 0 : _teacher_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_teacher_username = teacher.username) === null || _teacher_username === void 0 ? void 0 : _teacher_username.toLowerCase().includes(searchTerm.toLowerCase()));\n            });\n            console.log(\"Filtered results:\", filtered);\n            setFilteredTeachers(filtered);\n        } else {\n            setFilteredTeachers(teachers);\n        }\n    };\n    const addGrade = ()=>{\n        if (newGrade.grade && newGrade.minScore !== undefined && newGrade.maxScore !== undefined) {\n            const updatedGrades = [\n                ...gradingSystem.grades,\n                {\n                    ...newGrade,\n                    points: gradingSystem.grades.length\n                }\n            ];\n            setGradingSystem({\n                ...gradingSystem,\n                grades: updatedGrades\n            });\n            setNewGrade({});\n            setShowGradeModal(false);\n        }\n    };\n    const editGrade = (index)=>{\n        setNewGrade(gradingSystem.grades[index]);\n        setEditingGradeIndex(index);\n        setShowGradeModal(true);\n    };\n    const updateGrade = ()=>{\n        const updatedGrades = [\n            ...gradingSystem.grades\n        ];\n        updatedGrades[editingGradeIndex] = newGrade;\n        setGradingSystem({\n            ...gradingSystem,\n            grades: updatedGrades\n        });\n        setNewGrade({});\n        setEditingGradeIndex(-1);\n        setShowGradeModal(false);\n    };\n    const deleteGrade = (index)=>{\n        if (confirm(\"Are you sure you want to delete this grade?\")) {\n            const updatedGrades = gradingSystem.grades.filter((_, i)=>i !== index);\n            setGradingSystem({\n                ...gradingSystem,\n                grades: updatedGrades\n            });\n        }\n    };\n    const tabs = [\n        {\n            id: \"classes\",\n            name: \"Classes\",\n            icon: \"\\uD83C\\uDFEB\"\n        },\n        {\n            id: \"subjects\",\n            name: \"Subjects\",\n            icon: \"\\uD83D\\uDCDA\"\n        },\n        {\n            id: \"sessions\",\n            name: \"Sessions\",\n            icon: \"\\uD83D\\uDCC5\"\n        },\n        {\n            id: \"grading\",\n            name: \"Grading System\",\n            icon: \"\\uD83D\\uDCCA\"\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                allowedRoles: [\n                    \"admin\"\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-2\",\n                                                    children: \"Academic Setup\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100\",\n                                                    children: \"Configure classes, subjects, sessions, and grading system\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 273,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                className: \"w-12 h-12 text-white opacity-80\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 277,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 271,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Classes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: classes.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 286,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 285,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Subjects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: subjects.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-green-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 298,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 297,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Students\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: classes.reduce((sum, c)=>sum + c.currentStudents, 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-purple-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 310,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 309,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Current Session\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: (_sessions_find = sessions.find((s)=>s.status === \"current\")) === null || _sessions_find === void 0 ? void 0 : _sessions_find.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-orange-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 322,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 321,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 284,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:w-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                className: \"space-y-2\",\n                                                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveTab(tab.id),\n                                                        className: \"w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors \".concat(activeTab === tab.id ? \"bg-primary text-white\" : \"text-gray-700 hover:bg-gray-100\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: tab.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: tab.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, tab.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 337,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 336,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-8\",\n                                            children: [\n                                                activeTab === \"classes\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Class Management\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"class\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Class\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 370,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Class Name\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 378,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Level\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 379,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Students\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 380,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Capacity\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 381,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Class Teacher\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 382,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 383,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: classItem.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 389,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.level\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 390,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.currentStudents\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 391,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.capacity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 392,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.classTeacherName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium\",\n                                                                                                    children: classItem.classTeacherName\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 396,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: [\n                                                                                                        \"@\",\n                                                                                                        classItem.classTeacherUsername\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 397,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 395,\n                                                                                            columnNumber: 33\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400\",\n                                                                                            children: \"No teacher assigned\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 400,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 393,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 406,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 405,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>handleDelete(\"class\", classItem.id),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 412,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 408,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 404,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 403,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, classItem.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 388,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"subjects\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Subject Management\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"subject\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Subject\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 434,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Subject Name\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 442,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Code\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 443,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Category\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 444,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Credit Units\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 445,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Teacher\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 446,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 447,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 441,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 440,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: subject.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 453,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.code\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 454,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.category\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 455,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.creditUnits\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 456,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.teacher\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 457,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 461,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 460,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>handleDelete(\"subject\", subject.id),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 467,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 463,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 459,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 458,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, subject.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 452,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"sessions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Academic Sessions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"session\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 488,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Session\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 489,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                                            children: sessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border border-gray-200 rounded-lg p-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between items-start mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                                    children: session.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 497,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-3 py-1 text-xs font-semibold rounded-full \".concat(session.status === \"current\" ? \"bg-green-100 text-green-800\" : session.status === \"upcoming\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                                    children: session.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 498,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: \"Start:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 507,\n                                                                                            columnNumber: 30\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        new Date(session.startDate).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: \"End:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 508,\n                                                                                            columnNumber: 30\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        new Date(session.endDate).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 508,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 512,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 511,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleDelete(\"session\", session.id),\n                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 518,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 514,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 510,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, session.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"grading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Grading System Configuration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setNewGrade({});\n                                                                        setEditingGradeIndex(-1);\n                                                                        setShowGradeModal(true);\n                                                                    },\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 540,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Grade\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                            children: \"Grading Type\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 547,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: gradingSystem.type,\n                                                                            onChange: (e)=>setGradingSystem({\n                                                                                    ...gradingSystem,\n                                                                                    type: e.target.value\n                                                                                }),\n                                                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"percentage\",\n                                                                                    children: \"Percentage\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 553,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"points\",\n                                                                                    children: \"Points\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 554,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"letter\",\n                                                                                    children: \"Letter Grade\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 555,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                            children: \"Pass Mark (%)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            min: \"0\",\n                                                                            max: \"100\",\n                                                                            value: gradingSystem.passMarkPercentage,\n                                                                            onChange: (e)=>setGradingSystem({\n                                                                                    ...gradingSystem,\n                                                                                    passMarkPercentage: parseInt(e.target.value)\n                                                                                }),\n                                                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 560,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Grade\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 575,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Min Score\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 576,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Max Score\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 577,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Points\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 578,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Remark\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 579,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 580,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: (_gradingSystem_grades = gradingSystem.grades) === null || _gradingSystem_grades === void 0 ? void 0 : _gradingSystem_grades.map((grade, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: grade.grade\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 586,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.minScore\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 587,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.maxScore\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 588,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.points\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 589,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.remark\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 590,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>editGrade(index),\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 597,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 593,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>deleteGrade(index),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 603,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 599,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 592,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 591,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 585,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 583,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-end pt-6 border-t border-gray-200 mt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSave,\n                                                        disabled: isSaving,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors \".concat(isSaving ? \"bg-gray-400 cursor-not-allowed\" : \"bg-primary hover:bg-primary/90\", \" text-white\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SaveIcon, {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isSaving ? \"Saving...\" : \"Save Changes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 359,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 358,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 334,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                        lineNumber: 269,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: [\n                                \"Add New \",\n                                modalType\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 641,\n                            columnNumber: 15\n                        }, this),\n                        modalType === \"class\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Class Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 646,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            placeholder: \"e.g., SS1A\",\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 647,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 645,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 657,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.level || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    level: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS1\",\n                                                    children: \"JSS 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS2\",\n                                                    children: \"JSS 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS3\",\n                                                    children: \"JSS 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS1\",\n                                                    children: \"SS 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS2\",\n                                                    children: \"SS 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS3\",\n                                                    children: \"SS 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 658,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 656,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Class Teacher\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 674,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: teacherSearch,\n                                                    onChange: (e)=>handleTeacherSearch(e.target.value),\n                                                    onFocus: ()=>{\n                                                        console.log(\"Focus - Available teachers:\", teachers);\n                                                        setShowTeacherDropdown(true);\n                                                        setFilteredTeachers(teachers);\n                                                    },\n                                                    onBlur: ()=>setTimeout(()=>setShowTeacherDropdown(false), 200),\n                                                    placeholder: \"Search by username or name...\",\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 23\n                                                }, this),\n                                                showTeacherDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-50 w-full mt-1 max-h-48 overflow-y-auto bg-white border border-gray-200 rounded-lg shadow-lg\",\n                                                    children: filteredTeachers.length > 0 ? filteredTeachers.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onClick: ()=>{\n                                                                setSelectedTeacher(teacher);\n                                                                setTeacherSearch(\"\");\n                                                                setShowTeacherDropdown(false);\n                                                            },\n                                                            className: \"p-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: teacher.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-blue-600\",\n                                                                    children: [\n                                                                        \"@\",\n                                                                        teacher.username\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, teacher.uid, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 31\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 text-center text-gray-500\",\n                                                        children: teacherSearch ? \"No teachers found\" : \"Start typing to search teachers...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 675,\n                                            columnNumber: 21\n                                        }, this),\n                                        selectedTeacher && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-blue-900\",\n                                                    children: [\n                                                        \"Selected: \",\n                                                        selectedTeacher.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-700\",\n                                                    children: [\n                                                        \"@\",\n                                                        selectedTeacher.username\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 715,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 673,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 644,\n                            columnNumber: 17\n                        }, this),\n                        modalType === \"subject\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Subject Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 727,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 728,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 726,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Subject Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 737,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.code || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    code: e.target.value.toUpperCase()\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 738,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 736,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 747,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.category || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    category: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Core\",\n                                                    children: \"Core\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Science\",\n                                                    children: \"Science\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Arts\",\n                                                    children: \"Arts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Social Science\",\n                                                    children: \"Social Science\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Vocational\",\n                                                    children: \"Vocational\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 758,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 748,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 746,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 725,\n                            columnNumber: 17\n                        }, this),\n                        modalType === \"session\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 768,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            placeholder: \"e.g., 2024/2025\",\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 769,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 767,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Start Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 779,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            required: true,\n                                            value: newItem.startDate || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    startDate: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 780,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 778,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"End Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 789,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            required: true,\n                                            value: newItem.endDate || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    endDate: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 790,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 788,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 799,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.status || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    status: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 805,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"upcoming\",\n                                                    children: \"Upcoming\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"current\",\n                                                    children: \"Current\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 800,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 798,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 766,\n                            columnNumber: 17\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddItem,\n                                    disabled: isSaving,\n                                    className: \"flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50\",\n                                    children: isSaving ? \"Creating...\" : \"Create \".concat(modalType)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 815,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddModal(false),\n                                    className: \"flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 822,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 814,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 640,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 639,\n                columnNumber: 7\n            }, this),\n            showGradeModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: editingGradeIndex >= 0 ? \"Edit Grade\" : \"Add New Grade\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 837,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Grade Letter\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 840,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            maxLength: \"2\",\n                                            value: newGrade.grade || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    grade: e.target.value.toUpperCase()\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 841,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 839,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Min Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 851,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    value: newGrade.minScore || \"\",\n                                                    onChange: (e)=>setNewGrade({\n                                                            ...newGrade,\n                                                            minScore: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 850,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Max Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    value: newGrade.maxScore || \"\",\n                                                    onChange: (e)=>setNewGrade({\n                                                            ...newGrade,\n                                                            maxScore: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 861,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 849,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Points\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 874,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"10\",\n                                            value: newGrade.points || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    points: parseInt(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 875,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 873,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Remark\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 885,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: newGrade.remark || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    remark: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 886,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 884,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 838,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: editingGradeIndex >= 0 ? updateGrade : addGrade,\n                                    className: \"flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                    children: editingGradeIndex >= 0 ? \"Update Grade\" : \"Add Grade\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 895,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowGradeModal(false);\n                                        setNewGrade({});\n                                        setEditingGradeIndex(-1);\n                                    },\n                                    className: \"flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 901,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 894,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 836,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 835,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AdminAcademic, \"GW8DprATCrYqLKo1jDj/Go1raV0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c5 = AdminAcademic;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AcademicCapIcon\");\n$RefreshReg$(_c1, \"PlusIcon\");\n$RefreshReg$(_c2, \"EditIcon\");\n$RefreshReg$(_c3, \"TrashIcon\");\n$RefreshReg$(_c4, \"SaveIcon\");\n$RefreshReg$(_c5, \"AdminAcademic\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard/admin/academic.js\n"));

/***/ })

});