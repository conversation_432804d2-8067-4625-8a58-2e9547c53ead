"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard/admin/academic",{

/***/ "./pages/dashboard/admin/academic.js":
/*!*******************************************!*\
  !*** ./pages/dashboard/admin/academic.js ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminAcademic; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/DashboardLayout */ \"./components/DashboardLayout.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/ProtectedRoute */ \"./components/ProtectedRoute.js\");\n/* harmony import */ var _lib_academicService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../lib/academicService */ \"./lib/academicService.js\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../lib/authService */ \"./lib/authService.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Professional SVG Icons\nconst AcademicCapIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l9-5-9-5-9 5 9 5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 22,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined);\n};\n_c = AcademicCapIcon;\nconst PlusIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 28,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PlusIcon;\nconst EditIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EditIcon;\nconst TrashIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = TrashIcon;\nconst SaveIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3-3-3m3-3v12\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 46,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SaveIcon;\nfunction AdminAcademic() {\n    var _sessions_find, _gradingSystem_grades;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"classes\");\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gradingSystem, setGradingSystem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"percentage\",\n        passMarkPercentage: 40,\n        grades: [\n            {\n                grade: \"A\",\n                minScore: 80,\n                maxScore: 100,\n                points: 5,\n                remark: \"Excellent\"\n            },\n            {\n                grade: \"B\",\n                minScore: 70,\n                maxScore: 79,\n                points: 4,\n                remark: \"Very Good\"\n            },\n            {\n                grade: \"C\",\n                minScore: 60,\n                maxScore: 69,\n                points: 3,\n                remark: \"Good\"\n            },\n            {\n                grade: \"D\",\n                minScore: 50,\n                maxScore: 59,\n                points: 2,\n                remark: \"Pass\"\n            },\n            {\n                grade: \"E\",\n                minScore: 40,\n                maxScore: 49,\n                points: 1,\n                remark: \"Poor\"\n            },\n            {\n                grade: \"F\",\n                minScore: 0,\n                maxScore: 39,\n                points: 0,\n                remark: \"Fail\"\n            }\n        ]\n    });\n    const [showGradeModal, setShowGradeModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newGrade, setNewGrade] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editingGradeIndex, setEditingGradeIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalType, setModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherSearch, setTeacherSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedTeacher, setSelectedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newItem, setNewItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [filteredTeachers, setFilteredTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTeacherDropdown, setShowTeacherDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchingTeachers, setSearchingTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAcademicData();\n    }, []);\n    const loadAcademicData = async ()=>{\n        try {\n            console.log(\"Loading academic data...\");\n            const [classesData, subjectsData, sessionsData, teachersData] = await Promise.all([\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllClasses)(),\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllSubjects)(),\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllSessions)(),\n                getAllUsers()\n            ]);\n            // Filter teachers from all users\n            const teachersOnly = teachersData.filter((user)=>user.role === \"teacher\");\n            console.log(\"All users:\", teachersData.length, \"Teachers found:\", teachersOnly.length, teachersOnly);\n            setClasses(classesData);\n            setSubjects(subjectsData);\n            setSessions(sessionsData);\n            setTeachers(teachersOnly);\n            setFilteredTeachers(teachersOnly);\n        // Keep existing grading system\n        } catch (error) {\n            console.error(\"Error loading academic data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            // Mock API call - replace with actual implementation\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            alert(\"Academic setup saved successfully!\");\n        } catch (error) {\n            console.error(\"Error saving academic setup:\", error);\n            alert(\"Error saving academic setup. Please try again.\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleDelete = async (type, id)=>{\n        if (confirm(\"Are you sure you want to delete this \".concat(type, \"?\"))) {\n            try {\n                switch(type){\n                    case \"class\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteClass)(id);\n                        setClasses(classes.filter((c)=>c.id !== id));\n                        break;\n                    case \"subject\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteSubject)(id);\n                        setSubjects(subjects.filter((s)=>s.id !== id));\n                        break;\n                    case \"session\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteSession)(id);\n                        setSessions(sessions.filter((s)=>s.id !== id));\n                        break;\n                }\n                alert(\"\".concat(type, \" deleted successfully!\"));\n            } catch (error) {\n                console.error(\"Error deleting \".concat(type, \":\"), error);\n                alert(\"Error deleting \".concat(type, \". Please try again.\"));\n            }\n        }\n    };\n    const openAddModal = (type)=>{\n        setModalType(type);\n        setNewItem({});\n        setSelectedTeacher(null);\n        setTeacherSearch(\"\");\n        setFilteredTeachers(teachers) // Initialize with all teachers\n        ;\n        setShowTeacherDropdown(false);\n        setSearchingTeachers(false);\n        setShowAddModal(true);\n    };\n    const handleAddItem = async ()=>{\n        try {\n            setIsSaving(true);\n            let result;\n            switch(modalType){\n                case \"class\":\n                    const classData = {\n                        ...newItem,\n                        classTeacherId: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.uid,\n                        classTeacherName: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.name,\n                        classTeacherUsername: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.username,\n                        currentStudents: 0,\n                        capacity: 0 // Will be calculated based on enrolled students\n                    };\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createClass)(classData);\n                    setClasses([\n                        ...classes,\n                        result\n                    ]);\n                    break;\n                case \"subject\":\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createSubject)(newItem);\n                    setSubjects([\n                        ...subjects,\n                        result\n                    ]);\n                    break;\n                case \"session\":\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createSession)(newItem);\n                    setSessions([\n                        ...sessions,\n                        result\n                    ]);\n                    break;\n            }\n            setShowAddModal(false);\n            alert(\"\".concat(modalType, \" created successfully!\"));\n        } catch (error) {\n            console.error(\"Error creating \".concat(modalType, \":\"), error);\n            alert(\"Error creating \".concat(modalType, \". Please try again.\"));\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleTeacherSearch = (searchTerm)=>{\n        setTeacherSearch(searchTerm);\n        setShowTeacherDropdown(true);\n        if (searchTerm.trim().length > 0) {\n            // Filter teachers immediately without delay\n            const filtered = teachers.filter((teacher)=>{\n                var _teacher_name, _teacher_username, _teacher_email;\n                return ((_teacher_name = teacher.name) === null || _teacher_name === void 0 ? void 0 : _teacher_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_teacher_username = teacher.username) === null || _teacher_username === void 0 ? void 0 : _teacher_username.toLowerCase().includes(searchTerm.toLowerCase())) || ((_teacher_email = teacher.email) === null || _teacher_email === void 0 ? void 0 : _teacher_email.toLowerCase().includes(searchTerm.toLowerCase()));\n            });\n            setFilteredTeachers(filtered);\n        } else {\n            // Show all teachers when search is empty\n            setFilteredTeachers(teachers);\n        }\n        setSearchingTeachers(false);\n    };\n    const addGrade = ()=>{\n        if (newGrade.grade && newGrade.minScore !== undefined && newGrade.maxScore !== undefined) {\n            const updatedGrades = [\n                ...gradingSystem.grades,\n                {\n                    ...newGrade,\n                    points: gradingSystem.grades.length\n                }\n            ];\n            setGradingSystem({\n                ...gradingSystem,\n                grades: updatedGrades\n            });\n            setNewGrade({});\n            setShowGradeModal(false);\n        }\n    };\n    const editGrade = (index)=>{\n        setNewGrade(gradingSystem.grades[index]);\n        setEditingGradeIndex(index);\n        setShowGradeModal(true);\n    };\n    const updateGrade = ()=>{\n        const updatedGrades = [\n            ...gradingSystem.grades\n        ];\n        updatedGrades[editingGradeIndex] = newGrade;\n        setGradingSystem({\n            ...gradingSystem,\n            grades: updatedGrades\n        });\n        setNewGrade({});\n        setEditingGradeIndex(-1);\n        setShowGradeModal(false);\n    };\n    const deleteGrade = (index)=>{\n        if (confirm(\"Are you sure you want to delete this grade?\")) {\n            const updatedGrades = gradingSystem.grades.filter((_, i)=>i !== index);\n            setGradingSystem({\n                ...gradingSystem,\n                grades: updatedGrades\n            });\n        }\n    };\n    const tabs = [\n        {\n            id: \"classes\",\n            name: \"Classes\",\n            icon: \"\\uD83C\\uDFEB\"\n        },\n        {\n            id: \"subjects\",\n            name: \"Subjects\",\n            icon: \"\\uD83D\\uDCDA\"\n        },\n        {\n            id: \"sessions\",\n            name: \"Sessions\",\n            icon: \"\\uD83D\\uDCC5\"\n        },\n        {\n            id: \"grading\",\n            name: \"Grading System\",\n            icon: \"\\uD83D\\uDCCA\"\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 266,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 265,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 264,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                allowedRoles: [\n                    \"admin\"\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-2\",\n                                                    children: \"Academic Setup\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100\",\n                                                    children: \"Configure classes, subjects, sessions, and grading system\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 280,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                className: \"w-12 h-12 text-white opacity-80\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 284,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 279,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 278,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Classes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: classes.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 293,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 292,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Subjects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: subjects.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-green-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 305,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 304,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Students\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: classes.reduce((sum, c)=>sum + c.currentStudents, 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-purple-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 317,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 316,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Current Session\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: (_sessions_find = sessions.find((s)=>s.status === \"current\")) === null || _sessions_find === void 0 ? void 0 : _sessions_find.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-orange-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 329,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 328,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 291,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:w-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                className: \"space-y-2\",\n                                                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveTab(tab.id),\n                                                        className: \"w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors \".concat(activeTab === tab.id ? \"bg-primary text-white\" : \"text-gray-700 hover:bg-gray-100\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: tab.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: tab.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, tab.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 344,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 343,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-8\",\n                                            children: [\n                                                activeTab === \"classes\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Class Management\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"class\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Class\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Class Name\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 385,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Level\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 386,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Students\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 387,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Capacity\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 388,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Class Teacher\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 389,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 390,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: classItem.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 396,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.level\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 397,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.currentStudents\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 398,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.capacity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 399,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.classTeacherName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium\",\n                                                                                                    children: classItem.classTeacherName\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 403,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: [\n                                                                                                        \"@\",\n                                                                                                        classItem.classTeacherUsername\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 404,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 402,\n                                                                                            columnNumber: 33\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400\",\n                                                                                            children: \"No teacher assigned\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 407,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 400,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 413,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 412,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>handleDelete(\"class\", classItem.id),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 419,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 415,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 411,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 410,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, classItem.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 395,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"subjects\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Subject Management\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"subject\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 440,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Subject\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 441,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Subject Name\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 449,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Code\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Category\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 451,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Credit Units\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 452,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Teacher\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 453,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 454,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: subject.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 460,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.code\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 461,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.category\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 462,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.creditUnits\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 463,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.teacher\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 464,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 468,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 467,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>handleDelete(\"subject\", subject.id),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 474,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 470,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 466,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 465,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, subject.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 459,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 457,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"sessions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Academic Sessions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"session\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Session\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                                            children: sessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border border-gray-200 rounded-lg p-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between items-start mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                                    children: session.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 504,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-3 py-1 text-xs font-semibold rounded-full \".concat(session.status === \"current\" ? \"bg-green-100 text-green-800\" : session.status === \"upcoming\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                                    children: session.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: \"Start:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 514,\n                                                                                            columnNumber: 30\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        new Date(session.startDate).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 514,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: \"End:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 515,\n                                                                                            columnNumber: 30\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        new Date(session.endDate).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 515,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 519,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 518,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleDelete(\"session\", session.id),\n                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 525,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 521,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, session.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"grading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Grading System Configuration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setNewGrade({});\n                                                                        setEditingGradeIndex(-1);\n                                                                        setShowGradeModal(true);\n                                                                    },\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 547,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Grade\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                            children: \"Grading Type\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 554,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: gradingSystem.type,\n                                                                            onChange: (e)=>setGradingSystem({\n                                                                                    ...gradingSystem,\n                                                                                    type: e.target.value\n                                                                                }),\n                                                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"percentage\",\n                                                                                    children: \"Percentage\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 560,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"points\",\n                                                                                    children: \"Points\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 561,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"letter\",\n                                                                                    children: \"Letter Grade\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 562,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                            children: \"Pass Mark (%)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 566,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            min: \"0\",\n                                                                            max: \"100\",\n                                                                            value: gradingSystem.passMarkPercentage,\n                                                                            onChange: (e)=>setGradingSystem({\n                                                                                    ...gradingSystem,\n                                                                                    passMarkPercentage: parseInt(e.target.value)\n                                                                                }),\n                                                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 567,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Grade\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 582,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Min Score\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 583,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Max Score\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 584,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Points\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 585,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Remark\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 586,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 587,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 581,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 580,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: (_gradingSystem_grades = gradingSystem.grades) === null || _gradingSystem_grades === void 0 ? void 0 : _gradingSystem_grades.map((grade, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: grade.grade\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 593,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.minScore\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 594,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.maxScore\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 595,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.points\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 596,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.remark\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 597,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>editGrade(index),\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 604,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 600,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>deleteGrade(index),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 610,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 606,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 599,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 598,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-end pt-6 border-t border-gray-200 mt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSave,\n                                                        disabled: isSaving,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors \".concat(isSaving ? \"bg-gray-400 cursor-not-allowed\" : \"bg-primary hover:bg-primary/90\", \" text-white\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SaveIcon, {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isSaving ? \"Saving...\" : \"Save Changes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 366,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 365,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 341,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                        lineNumber: 276,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this),\n            showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: [\n                                \"Add New \",\n                                modalType\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 648,\n                            columnNumber: 15\n                        }, this),\n                        modalType === \"class\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Class Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 653,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            placeholder: \"e.g., SS1A\",\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 654,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 652,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 664,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.level || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    level: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS1\",\n                                                    children: \"JSS 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS2\",\n                                                    children: \"JSS 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS3\",\n                                                    children: \"JSS 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS1\",\n                                                    children: \"SS 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS2\",\n                                                    children: \"SS 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS3\",\n                                                    children: \"SS 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 665,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 663,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Class Teacher\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 681,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: teacherSearch,\n                                                    onChange: (e)=>handleTeacherSearch(e.target.value),\n                                                    onFocus: ()=>{\n                                                        setShowTeacherDropdown(true);\n                                                        if (teacherSearch.trim() === \"\") {\n                                                            setFilteredTeachers(teachers);\n                                                        }\n                                                    },\n                                                    onBlur: ()=>{\n                                                        // Delay hiding dropdown to allow clicking on options\n                                                        setTimeout(()=>{\n                                                            setShowTeacherDropdown(false);\n                                                        }, 150);\n                                                    },\n                                                    placeholder: \"Type teacher name or username...\",\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 23\n                                                }, this),\n                                                showTeacherDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto\",\n                                                    onMouseDown: (e)=>e.preventDefault(),\n                                                    children: searchingTeachers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"inline-flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                \"Searching...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 29\n                                                    }, this) : filteredTeachers.length > 0 ? filteredTeachers.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onClick: ()=>{\n                                                                setSelectedTeacher(teacher);\n                                                                setTeacherSearch(teacher.name) // Show selected teacher name in input\n                                                                ;\n                                                                setShowTeacherDropdown(false);\n                                                            },\n                                                            className: \"p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: teacher.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 725,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"@\",\n                                                                        teacher.username\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                teacher.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: teacher.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            ]\n                                                        }, teacher.uid, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 31\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 text-center text-gray-500\",\n                                                        children: teacherSearch ? \"No teachers found matching your search\" : \"Start typing to search teachers...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 682,\n                                            columnNumber: 21\n                                        }, this),\n                                        selectedTeacher && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-3 bg-blue-50 rounded-lg flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-blue-900\",\n                                                            children: [\n                                                                \"Selected: \",\n                                                                selectedTeacher.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-blue-700\",\n                                                            children: [\n                                                                \"@\",\n                                                                selectedTeacher.username\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 744,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 742,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        setSelectedTeacher(null);\n                                                        setTeacherSearch(\"\");\n                                                        setFilteredTeachers(teachers);\n                                                    },\n                                                    className: \"text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                                                    children: \"Clear\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 741,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 680,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 651,\n                            columnNumber: 17\n                        }, this),\n                        modalType === \"subject\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Subject Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 766,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 767,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 765,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Subject Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 776,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.code || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    code: e.target.value.toUpperCase()\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 777,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 775,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 786,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.category || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    category: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Core\",\n                                                    children: \"Core\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Science\",\n                                                    children: \"Science\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Arts\",\n                                                    children: \"Arts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Social Science\",\n                                                    children: \"Social Science\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Vocational\",\n                                                    children: \"Vocational\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 797,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 787,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 785,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 764,\n                            columnNumber: 17\n                        }, this),\n                        modalType === \"session\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 807,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            placeholder: \"e.g., 2024/2025\",\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 808,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 806,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Start Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 818,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            required: true,\n                                            value: newItem.startDate || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    startDate: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 819,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 817,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"End Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 828,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            required: true,\n                                            value: newItem.endDate || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    endDate: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 829,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 827,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 838,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.status || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    status: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"upcoming\",\n                                                    children: \"Upcoming\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"current\",\n                                                    children: \"Current\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 839,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 837,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 805,\n                            columnNumber: 17\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddItem,\n                                    disabled: isSaving,\n                                    className: \"flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50\",\n                                    children: isSaving ? \"Creating...\" : \"Create \".concat(modalType)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 854,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddModal(false),\n                                    className: \"flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 861,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 853,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 647,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 646,\n                columnNumber: 7\n            }, this),\n            showGradeModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: editingGradeIndex >= 0 ? \"Edit Grade\" : \"Add New Grade\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 876,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Grade Letter\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 879,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            maxLength: \"2\",\n                                            value: newGrade.grade || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    grade: e.target.value.toUpperCase()\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 880,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 878,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Min Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    value: newGrade.minScore || \"\",\n                                                    onChange: (e)=>setNewGrade({\n                                                            ...newGrade,\n                                                            minScore: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 889,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Max Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    value: newGrade.maxScore || \"\",\n                                                    onChange: (e)=>setNewGrade({\n                                                            ...newGrade,\n                                                            maxScore: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 902,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 900,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 888,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Points\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 913,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"10\",\n                                            value: newGrade.points || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    points: parseInt(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 914,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 912,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Remark\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 924,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: newGrade.remark || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    remark: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 925,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 923,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 877,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: editingGradeIndex >= 0 ? updateGrade : addGrade,\n                                    className: \"flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                    children: editingGradeIndex >= 0 ? \"Update Grade\" : \"Add Grade\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 934,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowGradeModal(false);\n                                        setNewGrade({});\n                                        setEditingGradeIndex(-1);\n                                    },\n                                    className: \"flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 940,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 933,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 875,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 874,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AdminAcademic, \"KH0hLxw8rxzDpY+w53qUWHcONz8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c5 = AdminAcademic;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AcademicCapIcon\");\n$RefreshReg$(_c1, \"PlusIcon\");\n$RefreshReg$(_c2, \"EditIcon\");\n$RefreshReg$(_c3, \"TrashIcon\");\n$RefreshReg$(_c4, \"SaveIcon\");\n$RefreshReg$(_c5, \"AdminAcademic\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard/admin/academic.js\n"));

/***/ })

});