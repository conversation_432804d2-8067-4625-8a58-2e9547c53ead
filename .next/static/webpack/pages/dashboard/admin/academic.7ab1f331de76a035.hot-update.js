"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard/admin/academic",{

/***/ "./pages/dashboard/admin/academic.js":
/*!*******************************************!*\
  !*** ./pages/dashboard/admin/academic.js ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminAcademic; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/DashboardLayout */ \"./components/DashboardLayout.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/ProtectedRoute */ \"./components/ProtectedRoute.js\");\n/* harmony import */ var _lib_academicService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../lib/academicService */ \"./lib/academicService.js\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../lib/authService */ \"./lib/authService.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Professional SVG Icons\nconst AcademicCapIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l9-5-9-5-9 5 9 5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 22,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined);\n};\n_c = AcademicCapIcon;\nconst PlusIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 28,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PlusIcon;\nconst EditIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EditIcon;\nconst TrashIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = TrashIcon;\nconst SaveIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3-3-3m3-3v12\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 46,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SaveIcon;\nfunction AdminAcademic() {\n    var _sessions_find, _gradingSystem_grades;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"classes\");\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gradingSystem, setGradingSystem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"percentage\",\n        passMarkPercentage: 40,\n        grades: [\n            {\n                grade: \"A\",\n                minScore: 80,\n                maxScore: 100,\n                points: 5,\n                remark: \"Excellent\"\n            },\n            {\n                grade: \"B\",\n                minScore: 70,\n                maxScore: 79,\n                points: 4,\n                remark: \"Very Good\"\n            },\n            {\n                grade: \"C\",\n                minScore: 60,\n                maxScore: 69,\n                points: 3,\n                remark: \"Good\"\n            },\n            {\n                grade: \"D\",\n                minScore: 50,\n                maxScore: 59,\n                points: 2,\n                remark: \"Pass\"\n            },\n            {\n                grade: \"E\",\n                minScore: 40,\n                maxScore: 49,\n                points: 1,\n                remark: \"Poor\"\n            },\n            {\n                grade: \"F\",\n                minScore: 0,\n                maxScore: 39,\n                points: 0,\n                remark: \"Fail\"\n            }\n        ]\n    });\n    const [showGradeModal, setShowGradeModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newGrade, setNewGrade] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editingGradeIndex, setEditingGradeIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalType, setModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherSearch, setTeacherSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedTeacher, setSelectedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newItem, setNewItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [filteredTeachers, setFilteredTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTeacherDropdown, setShowTeacherDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchingTeachers, setSearchingTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAcademicData();\n    }, []);\n    const loadAcademicData = async ()=>{\n        try {\n            console.log(\"Loading academic data...\");\n            const [classesData, subjectsData, sessionsData, teachersData] = await Promise.all([\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllClasses)(),\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllSubjects)(),\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllSessions)(),\n                getAllUsers()\n            ]);\n            // Debug: Log all users first\n            console.log(\"DEBUG - All users loaded:\", teachersData.length, teachersData);\n            // Filter teachers from all users\n            const teachersOnly = teachersData.filter((user)=>user.role === \"teacher\");\n            console.log(\"DEBUG - Teachers found:\", teachersOnly.length, teachersOnly);\n            // Debug: Check what roles exist\n            const allRoles = [\n                ...new Set(teachersData.map((user)=>user.role))\n            ];\n            console.log(\"DEBUG - All roles in database:\", allRoles);\n            setClasses(classesData);\n            setSubjects(subjectsData);\n            setSessions(sessionsData);\n            setTeachers(teachersOnly);\n            setFilteredTeachers(teachersOnly);\n        // Keep existing grading system\n        } catch (error) {\n            console.error(\"Error loading academic data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            // Mock API call - replace with actual implementation\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            alert(\"Academic setup saved successfully!\");\n        } catch (error) {\n            console.error(\"Error saving academic setup:\", error);\n            alert(\"Error saving academic setup. Please try again.\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleDelete = async (type, id)=>{\n        if (confirm(\"Are you sure you want to delete this \".concat(type, \"?\"))) {\n            try {\n                switch(type){\n                    case \"class\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteClass)(id);\n                        setClasses(classes.filter((c)=>c.id !== id));\n                        break;\n                    case \"subject\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteSubject)(id);\n                        setSubjects(subjects.filter((s)=>s.id !== id));\n                        break;\n                    case \"session\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteSession)(id);\n                        setSessions(sessions.filter((s)=>s.id !== id));\n                        break;\n                }\n                alert(\"\".concat(type, \" deleted successfully!\"));\n            } catch (error) {\n                console.error(\"Error deleting \".concat(type, \":\"), error);\n                alert(\"Error deleting \".concat(type, \". Please try again.\"));\n            }\n        }\n    };\n    const openAddModal = (type)=>{\n        setModalType(type);\n        setNewItem({});\n        setSelectedTeacher(null);\n        setTeacherSearch(\"\");\n        setFilteredTeachers(teachers) // Initialize with all teachers\n        ;\n        setShowTeacherDropdown(false);\n        setSearchingTeachers(false);\n        setShowAddModal(true);\n    };\n    const handleAddItem = async ()=>{\n        try {\n            setIsSaving(true);\n            let result;\n            switch(modalType){\n                case \"class\":\n                    const classData = {\n                        ...newItem,\n                        classTeacherId: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.uid,\n                        classTeacherName: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.name,\n                        classTeacherUsername: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.username,\n                        currentStudents: 0,\n                        capacity: 0 // Will be calculated based on enrolled students\n                    };\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createClass)(classData);\n                    setClasses([\n                        ...classes,\n                        result\n                    ]);\n                    break;\n                case \"subject\":\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createSubject)(newItem);\n                    setSubjects([\n                        ...subjects,\n                        result\n                    ]);\n                    break;\n                case \"session\":\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createSession)(newItem);\n                    setSessions([\n                        ...sessions,\n                        result\n                    ]);\n                    break;\n            }\n            setShowAddModal(false);\n            alert(\"\".concat(modalType, \" created successfully!\"));\n        } catch (error) {\n            console.error(\"Error creating \".concat(modalType, \":\"), error);\n            alert(\"Error creating \".concat(modalType, \". Please try again.\"));\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleTeacherSearch = (searchTerm)=>{\n        setTeacherSearch(searchTerm);\n        setShowTeacherDropdown(true);\n        console.log(\"DEBUG - Search term:\", searchTerm);\n        console.log(\"DEBUG - Available teachers for search:\", teachers.length, teachers);\n        if (searchTerm.trim().length > 0) {\n            setSearchingTeachers(true);\n            // Small delay to show spinner as you intended\n            setTimeout(()=>{\n                const filtered = teachers.filter((teacher)=>{\n                    var _teacher_name, _teacher_username, _teacher_email;\n                    const nameMatch = (_teacher_name = teacher.name) === null || _teacher_name === void 0 ? void 0 : _teacher_name.toLowerCase().includes(searchTerm.toLowerCase());\n                    const usernameMatch = (_teacher_username = teacher.username) === null || _teacher_username === void 0 ? void 0 : _teacher_username.toLowerCase().includes(searchTerm.toLowerCase());\n                    const emailMatch = (_teacher_email = teacher.email) === null || _teacher_email === void 0 ? void 0 : _teacher_email.toLowerCase().includes(searchTerm.toLowerCase());\n                    console.log(\"DEBUG - Teacher:\", teacher.name, \"Name match:\", nameMatch, \"Username match:\", usernameMatch, \"Email match:\", emailMatch);\n                    return nameMatch || usernameMatch || emailMatch;\n                });\n                console.log(\"DEBUG - Filtered results:\", filtered.length, filtered);\n                setFilteredTeachers(filtered);\n                setSearchingTeachers(false);\n            }, 300);\n        } else {\n            // Show all teachers when search is empty\n            setFilteredTeachers(teachers);\n            setSearchingTeachers(false);\n        }\n    };\n    const addGrade = ()=>{\n        if (newGrade.grade && newGrade.minScore !== undefined && newGrade.maxScore !== undefined) {\n            const updatedGrades = [\n                ...gradingSystem.grades,\n                {\n                    ...newGrade,\n                    points: gradingSystem.grades.length\n                }\n            ];\n            setGradingSystem({\n                ...gradingSystem,\n                grades: updatedGrades\n            });\n            setNewGrade({});\n            setShowGradeModal(false);\n        }\n    };\n    const editGrade = (index)=>{\n        setNewGrade(gradingSystem.grades[index]);\n        setEditingGradeIndex(index);\n        setShowGradeModal(true);\n    };\n    const updateGrade = ()=>{\n        const updatedGrades = [\n            ...gradingSystem.grades\n        ];\n        updatedGrades[editingGradeIndex] = newGrade;\n        setGradingSystem({\n            ...gradingSystem,\n            grades: updatedGrades\n        });\n        setNewGrade({});\n        setEditingGradeIndex(-1);\n        setShowGradeModal(false);\n    };\n    const deleteGrade = (index)=>{\n        if (confirm(\"Are you sure you want to delete this grade?\")) {\n            const updatedGrades = gradingSystem.grades.filter((_, i)=>i !== index);\n            setGradingSystem({\n                ...gradingSystem,\n                grades: updatedGrades\n            });\n        }\n    };\n    const tabs = [\n        {\n            id: \"classes\",\n            name: \"Classes\",\n            icon: \"\\uD83C\\uDFEB\"\n        },\n        {\n            id: \"subjects\",\n            name: \"Subjects\",\n            icon: \"\\uD83D\\uDCDA\"\n        },\n        {\n            id: \"sessions\",\n            name: \"Sessions\",\n            icon: \"\\uD83D\\uDCC5\"\n        },\n        {\n            id: \"grading\",\n            name: \"Grading System\",\n            icon: \"\\uD83D\\uDCCA\"\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 286,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 285,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 284,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                allowedRoles: [\n                    \"admin\"\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-2\",\n                                                    children: \"Academic Setup\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100\",\n                                                    children: \"Configure classes, subjects, sessions, and grading system\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 300,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                className: \"w-12 h-12 text-white opacity-80\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 304,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 299,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 298,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Classes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: classes.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 313,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 312,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Subjects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: subjects.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-green-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 325,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 324,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Students\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: classes.reduce((sum, c)=>sum + c.currentStudents, 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-purple-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 337,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 336,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Current Session\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: (_sessions_find = sessions.find((s)=>s.status === \"current\")) === null || _sessions_find === void 0 ? void 0 : _sessions_find.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-orange-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 349,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 348,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 311,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:w-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                className: \"space-y-2\",\n                                                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveTab(tab.id),\n                                                        className: \"w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors \".concat(activeTab === tab.id ? \"bg-primary text-white\" : \"text-gray-700 hover:bg-gray-100\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: tab.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: tab.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, tab.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                lineNumber: 365,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 364,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 363,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-8\",\n                                            children: [\n                                                activeTab === \"classes\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Class Management\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"class\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Class\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Class Name\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 405,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Level\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 406,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Students\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 407,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Capacity\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 408,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Class Teacher\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 409,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 410,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 404,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: classItem.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 416,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.level\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 417,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.currentStudents\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 418,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.capacity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 419,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.classTeacherName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium\",\n                                                                                                    children: classItem.classTeacherName\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 423,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: [\n                                                                                                        \"@\",\n                                                                                                        classItem.classTeacherUsername\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 424,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 422,\n                                                                                            columnNumber: 33\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400\",\n                                                                                            children: \"No teacher assigned\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 427,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 420,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 433,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 432,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>handleDelete(\"class\", classItem.id),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 439,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 435,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 431,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 430,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, classItem.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 415,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"subjects\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Subject Management\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"subject\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 460,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Subject\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 461,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Subject Name\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 469,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Code\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 470,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Category\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 471,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Credit Units\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 472,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Teacher\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 473,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 474,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 468,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: subject.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 480,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.code\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 481,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.category\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 482,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.creditUnits\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 483,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.teacher\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 484,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 488,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 487,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>handleDelete(\"subject\", subject.id),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 494,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 490,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 486,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 485,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, subject.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 479,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"sessions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Academic Sessions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"session\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 515,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Session\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 516,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                                            children: sessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border border-gray-200 rounded-lg p-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between items-start mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                                    children: session.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 524,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-3 py-1 text-xs font-semibold rounded-full \".concat(session.status === \"current\" ? \"bg-green-100 text-green-800\" : session.status === \"upcoming\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                                    children: session.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 525,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 523,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: \"Start:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 534,\n                                                                                            columnNumber: 30\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        new Date(session.startDate).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 534,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: \"End:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 535,\n                                                                                            columnNumber: 30\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        new Date(session.endDate).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 535,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 539,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleDelete(\"session\", session.id),\n                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 545,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 541,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, session.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"grading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Grading System Configuration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setNewGrade({});\n                                                                        setEditingGradeIndex(-1);\n                                                                        setShowGradeModal(true);\n                                                                    },\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 567,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Grade\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                            children: \"Grading Type\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: gradingSystem.type,\n                                                                            onChange: (e)=>setGradingSystem({\n                                                                                    ...gradingSystem,\n                                                                                    type: e.target.value\n                                                                                }),\n                                                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"percentage\",\n                                                                                    children: \"Percentage\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 580,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"points\",\n                                                                                    children: \"Points\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 581,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"letter\",\n                                                                                    children: \"Letter Grade\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 582,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                            children: \"Pass Mark (%)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            min: \"0\",\n                                                                            max: \"100\",\n                                                                            value: gradingSystem.passMarkPercentage,\n                                                                            onChange: (e)=>setGradingSystem({\n                                                                                    ...gradingSystem,\n                                                                                    passMarkPercentage: parseInt(e.target.value)\n                                                                                }),\n                                                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 587,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Grade\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 602,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Min Score\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 603,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Max Score\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 604,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Points\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 605,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Remark\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 606,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 607,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: (_gradingSystem_grades = gradingSystem.grades) === null || _gradingSystem_grades === void 0 ? void 0 : _gradingSystem_grades.map((grade, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: grade.grade\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 613,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.minScore\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 614,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.maxScore\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 615,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.points\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 616,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.remark\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 617,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>editGrade(index),\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 624,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 620,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>deleteGrade(index),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 630,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 626,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 619,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 618,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 612,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 610,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-end pt-6 border-t border-gray-200 mt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSave,\n                                                        disabled: isSaving,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors \".concat(isSaving ? \"bg-gray-400 cursor-not-allowed\" : \"bg-primary hover:bg-primary/90\", \" text-white\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SaveIcon, {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isSaving ? \"Saving...\" : \"Save Changes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 386,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 385,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 361,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                        lineNumber: 296,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: [\n                                \"Add New \",\n                                modalType\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 668,\n                            columnNumber: 15\n                        }, this),\n                        modalType === \"class\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Class Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 673,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            placeholder: \"e.g., SS1A\",\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 674,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 672,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 684,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.level || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    level: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS1\",\n                                                    children: \"JSS 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS2\",\n                                                    children: \"JSS 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS3\",\n                                                    children: \"JSS 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS1\",\n                                                    children: \"SS 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS2\",\n                                                    children: \"SS 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS3\",\n                                                    children: \"SS 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 685,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 683,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Class Teacher\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 701,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: teacherSearch,\n                                                    onChange: (e)=>handleTeacherSearch(e.target.value),\n                                                    onFocus: ()=>{\n                                                        setShowTeacherDropdown(true);\n                                                        if (teacherSearch.trim() === \"\") {\n                                                            setFilteredTeachers(teachers);\n                                                        }\n                                                    },\n                                                    onBlur: ()=>{\n                                                        // Delay hiding dropdown to allow clicking on options\n                                                        setTimeout(()=>{\n                                                            setShowTeacherDropdown(false);\n                                                        }, 150);\n                                                    },\n                                                    placeholder: \"Type teacher name or username...\",\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 23\n                                                }, this),\n                                                showTeacherDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto\",\n                                                    onMouseDown: (e)=>e.preventDefault(),\n                                                    children: searchingTeachers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"inline-flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                \"Searching...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 29\n                                                    }, this) : filteredTeachers.length > 0 ? filteredTeachers.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onClick: ()=>{\n                                                                setSelectedTeacher(teacher);\n                                                                setTeacherSearch(teacher.name) // Show selected teacher name in input\n                                                                ;\n                                                                setShowTeacherDropdown(false);\n                                                            },\n                                                            className: \"p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: teacher.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"@\",\n                                                                        teacher.username\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                teacher.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: teacher.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            ]\n                                                        }, teacher.uid, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 31\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 text-center text-gray-500\",\n                                                        children: teacherSearch ? \"No teachers found matching your search\" : \"Start typing to search teachers...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 702,\n                                            columnNumber: 21\n                                        }, this),\n                                        selectedTeacher && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-3 bg-blue-50 rounded-lg flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-blue-900\",\n                                                            children: [\n                                                                \"Selected: \",\n                                                                selectedTeacher.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 763,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-blue-700\",\n                                                            children: [\n                                                                \"@\",\n                                                                selectedTeacher.username\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        setSelectedTeacher(null);\n                                                        setTeacherSearch(\"\");\n                                                        setFilteredTeachers(teachers);\n                                                    },\n                                                    className: \"text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                                                    children: \"Clear\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 761,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 700,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 671,\n                            columnNumber: 17\n                        }, this),\n                        modalType === \"subject\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Subject Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 786,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 787,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 785,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Subject Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 796,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.code || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    code: e.target.value.toUpperCase()\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 797,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 795,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 806,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.category || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    category: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Core\",\n                                                    children: \"Core\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Science\",\n                                                    children: \"Science\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Arts\",\n                                                    children: \"Arts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Social Science\",\n                                                    children: \"Social Science\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 816,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Vocational\",\n                                                    children: \"Vocational\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 807,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 805,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 784,\n                            columnNumber: 17\n                        }, this),\n                        modalType === \"session\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 827,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            placeholder: \"e.g., 2024/2025\",\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 828,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 826,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Start Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 838,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            required: true,\n                                            value: newItem.startDate || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    startDate: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 839,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 837,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"End Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 848,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            required: true,\n                                            value: newItem.endDate || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    endDate: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 849,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 847,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 858,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.status || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    status: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 864,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"upcoming\",\n                                                    children: \"Upcoming\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 865,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"current\",\n                                                    children: \"Current\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 859,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 857,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 825,\n                            columnNumber: 17\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddItem,\n                                    disabled: isSaving,\n                                    className: \"flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50\",\n                                    children: isSaving ? \"Creating...\" : \"Create \".concat(modalType)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 874,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddModal(false),\n                                    className: \"flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 881,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 873,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 667,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 666,\n                columnNumber: 7\n            }, this),\n            showGradeModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: editingGradeIndex >= 0 ? \"Edit Grade\" : \"Add New Grade\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 896,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Grade Letter\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 899,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            maxLength: \"2\",\n                                            value: newGrade.grade || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    grade: e.target.value.toUpperCase()\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 900,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 898,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Min Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    value: newGrade.minScore || \"\",\n                                                    onChange: (e)=>setNewGrade({\n                                                            ...newGrade,\n                                                            minScore: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 911,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 909,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Max Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 921,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    value: newGrade.maxScore || \"\",\n                                                    onChange: (e)=>setNewGrade({\n                                                            ...newGrade,\n                                                            maxScore: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 920,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 908,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Points\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 933,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"10\",\n                                            value: newGrade.points || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    points: parseInt(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 934,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 932,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Remark\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 944,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: newGrade.remark || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    remark: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 945,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 943,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 897,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: editingGradeIndex >= 0 ? updateGrade : addGrade,\n                                    className: \"flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                    children: editingGradeIndex >= 0 ? \"Update Grade\" : \"Add Grade\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 954,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowGradeModal(false);\n                                        setNewGrade({});\n                                        setEditingGradeIndex(-1);\n                                    },\n                                    className: \"flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 960,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 953,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 895,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 894,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AdminAcademic, \"KH0hLxw8rxzDpY+w53qUWHcONz8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c5 = AdminAcademic;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AcademicCapIcon\");\n$RefreshReg$(_c1, \"PlusIcon\");\n$RefreshReg$(_c2, \"EditIcon\");\n$RefreshReg$(_c3, \"TrashIcon\");\n$RefreshReg$(_c4, \"SaveIcon\");\n$RefreshReg$(_c5, \"AdminAcademic\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard/admin/academic.js\n"));

/***/ })

});