"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard/admin/academic",{

/***/ "./pages/dashboard/admin/academic.js":
/*!*******************************************!*\
  !*** ./pages/dashboard/admin/academic.js ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminAcademic; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/DashboardLayout */ \"./components/DashboardLayout.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/ProtectedRoute */ \"./components/ProtectedRoute.js\");\n/* harmony import */ var _lib_academicService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../lib/academicService */ \"./lib/academicService.js\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../lib/authService */ \"./lib/authService.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Professional SVG Icons\nconst AcademicCapIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l9-5-9-5-9 5 9 5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 22,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined);\n};\n_c = AcademicCapIcon;\nconst PlusIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 28,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = PlusIcon;\nconst EditIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = EditIcon;\nconst TrashIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = TrashIcon;\nconst SaveIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3-3-3m3-3v12\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 46,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SaveIcon;\nfunction AdminAcademic() {\n    var _sessions_find, _gradingSystem_grades;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"classes\");\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gradingSystem, setGradingSystem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"percentage\",\n        passMarkPercentage: 40,\n        grades: [\n            {\n                grade: \"A\",\n                minScore: 80,\n                maxScore: 100,\n                points: 5,\n                remark: \"Excellent\"\n            },\n            {\n                grade: \"B\",\n                minScore: 70,\n                maxScore: 79,\n                points: 4,\n                remark: \"Very Good\"\n            },\n            {\n                grade: \"C\",\n                minScore: 60,\n                maxScore: 69,\n                points: 3,\n                remark: \"Good\"\n            },\n            {\n                grade: \"D\",\n                minScore: 50,\n                maxScore: 59,\n                points: 2,\n                remark: \"Pass\"\n            },\n            {\n                grade: \"E\",\n                minScore: 40,\n                maxScore: 49,\n                points: 1,\n                remark: \"Poor\"\n            },\n            {\n                grade: \"F\",\n                minScore: 0,\n                maxScore: 39,\n                points: 0,\n                remark: \"Fail\"\n            }\n        ]\n    });\n    const [showGradeModal, setShowGradeModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newGrade, setNewGrade] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editingGradeIndex, setEditingGradeIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalType, setModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherSearch, setTeacherSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedTeacher, setSelectedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newItem, setNewItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [filteredTeachers, setFilteredTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTeacherDropdown, setShowTeacherDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAcademicData();\n    }, []);\n    const loadAcademicData = async ()=>{\n        try {\n            console.log(\"Loading academic data...\");\n            const [classesData, subjectsData, sessionsData, teachersData] = await Promise.all([\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllClasses)(),\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllSubjects)(),\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllSessions)(),\n                (0,_lib_authService__WEBPACK_IMPORTED_MODULE_6__.getUsersByRole)(\"teacher\")\n            ]);\n            console.log(\"Teachers loaded:\", teachersData.length, teachersData);\n            setClasses(classesData);\n            setSubjects(subjectsData);\n            setSessions(sessionsData);\n            setTeachers(teachersData);\n            setFilteredTeachers(teachersData);\n        // Keep existing grading system\n        } catch (error) {\n            console.error(\"Error loading academic data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            // Mock API call - replace with actual implementation\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            alert(\"Academic setup saved successfully!\");\n        } catch (error) {\n            console.error(\"Error saving academic setup:\", error);\n            alert(\"Error saving academic setup. Please try again.\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleDelete = async (type, id)=>{\n        if (confirm(\"Are you sure you want to delete this \".concat(type, \"?\"))) {\n            try {\n                switch(type){\n                    case \"class\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteClass)(id);\n                        setClasses(classes.filter((c)=>c.id !== id));\n                        break;\n                    case \"subject\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteSubject)(id);\n                        setSubjects(subjects.filter((s)=>s.id !== id));\n                        break;\n                    case \"session\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteSession)(id);\n                        setSessions(sessions.filter((s)=>s.id !== id));\n                        break;\n                }\n                alert(\"\".concat(type, \" deleted successfully!\"));\n            } catch (error) {\n                console.error(\"Error deleting \".concat(type, \":\"), error);\n                alert(\"Error deleting \".concat(type, \". Please try again.\"));\n            }\n        }\n    };\n    const openAddModal = (type)=>{\n        setModalType(type);\n        setNewItem({});\n        setSelectedTeacher(null);\n        setTeacherSearch(\"\");\n        setFilteredTeachers([]);\n        setShowTeacherDropdown(false);\n        setShowAddModal(true);\n    };\n    const handleAddItem = async ()=>{\n        try {\n            setIsSaving(true);\n            let result;\n            switch(modalType){\n                case \"class\":\n                    const classData = {\n                        ...newItem,\n                        classTeacherId: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.uid,\n                        classTeacherName: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.name,\n                        classTeacherUsername: selectedTeacher === null || selectedTeacher === void 0 ? void 0 : selectedTeacher.username,\n                        currentStudents: 0,\n                        capacity: 0 // Will be calculated based on enrolled students\n                    };\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createClass)(classData);\n                    setClasses([\n                        ...classes,\n                        result\n                    ]);\n                    break;\n                case \"subject\":\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createSubject)(newItem);\n                    setSubjects([\n                        ...subjects,\n                        result\n                    ]);\n                    break;\n                case \"session\":\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createSession)(newItem);\n                    setSessions([\n                        ...sessions,\n                        result\n                    ]);\n                    break;\n            }\n            setShowAddModal(false);\n            alert(\"\".concat(modalType, \" created successfully!\"));\n        } catch (error) {\n            console.error(\"Error creating \".concat(modalType, \":\"), error);\n            alert(\"Error creating \".concat(modalType, \". Please try again.\"));\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleTeacherSearch = (searchTerm)=>{\n        console.log(\"Searching for:\", searchTerm, \"Available teachers:\", teachers.length);\n        setTeacherSearch(searchTerm);\n        setShowTeacherDropdown(true);\n        if (searchTerm.trim().length > 0) {\n            const filtered = teachers.filter((teacher)=>{\n                var _teacher_name, _teacher_username;\n                const nameMatch = (_teacher_name = teacher.name) === null || _teacher_name === void 0 ? void 0 : _teacher_name.toLowerCase().includes(searchTerm.toLowerCase());\n                const usernameMatch = (_teacher_username = teacher.username) === null || _teacher_username === void 0 ? void 0 : _teacher_username.toLowerCase().includes(searchTerm.toLowerCase());\n                console.log(\"Checking \".concat(teacher.name, \" (@\").concat(teacher.username, \"):\"), {\n                    nameMatch,\n                    usernameMatch\n                });\n                return nameMatch || usernameMatch;\n            });\n            console.log(\"Filtered results:\", filtered.length, filtered);\n            setFilteredTeachers(filtered);\n        } else {\n            setFilteredTeachers(teachers);\n        }\n    };\n    const addGrade = ()=>{\n        if (newGrade.grade && newGrade.minScore !== undefined && newGrade.maxScore !== undefined) {\n            const updatedGrades = [\n                ...gradingSystem.grades,\n                {\n                    ...newGrade,\n                    points: gradingSystem.grades.length\n                }\n            ];\n            setGradingSystem({\n                ...gradingSystem,\n                grades: updatedGrades\n            });\n            setNewGrade({});\n            setShowGradeModal(false);\n        }\n    };\n    const editGrade = (index)=>{\n        setNewGrade(gradingSystem.grades[index]);\n        setEditingGradeIndex(index);\n        setShowGradeModal(true);\n    };\n    const updateGrade = ()=>{\n        const updatedGrades = [\n            ...gradingSystem.grades\n        ];\n        updatedGrades[editingGradeIndex] = newGrade;\n        setGradingSystem({\n            ...gradingSystem,\n            grades: updatedGrades\n        });\n        setNewGrade({});\n        setEditingGradeIndex(-1);\n        setShowGradeModal(false);\n    };\n    const deleteGrade = (index)=>{\n        if (confirm(\"Are you sure you want to delete this grade?\")) {\n            const updatedGrades = gradingSystem.grades.filter((_, i)=>i !== index);\n            setGradingSystem({\n                ...gradingSystem,\n                grades: updatedGrades\n            });\n        }\n    };\n    const tabs = [\n        {\n            id: \"classes\",\n            name: \"Classes\",\n            icon: \"\\uD83C\\uDFEB\"\n        },\n        {\n            id: \"subjects\",\n            name: \"Subjects\",\n            icon: \"\\uD83D\\uDCDA\"\n        },\n        {\n            id: \"sessions\",\n            name: \"Sessions\",\n            icon: \"\\uD83D\\uDCC5\"\n        },\n        {\n            id: \"grading\",\n            name: \"Grading System\",\n            icon: \"\\uD83D\\uDCCA\"\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 261,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                allowedRoles: [\n                    \"admin\"\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-2\",\n                                                    children: \"Academic Setup\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100\",\n                                                    children: \"Configure classes, subjects, sessions, and grading system\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 275,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                className: \"w-12 h-12 text-white opacity-80\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                lineNumber: 280,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 279,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 274,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 273,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Classes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: classes.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 288,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 287,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Subjects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: subjects.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-green-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 300,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 299,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Students\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: classes.reduce((sum, c)=>sum + c.currentStudents, 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-purple-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 312,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 311,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Current Session\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: (_sessions_find = sessions.find((s)=>s.status === \"current\")) === null || _sessions_find === void 0 ? void 0 : _sessions_find.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-orange-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 324,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 323,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 286,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:w-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                className: \"space-y-2\",\n                                                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveTab(tab.id),\n                                                        className: \"w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors \".concat(activeTab === tab.id ? \"bg-primary text-white\" : \"text-gray-700 hover:bg-gray-100\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: tab.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: tab.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, tab.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 339,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 338,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-8\",\n                                            children: [\n                                                activeTab === \"classes\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Class Management\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"class\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Class\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Class Name\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 380,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Level\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 381,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Students\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 382,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Capacity\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 383,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Class Teacher\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 384,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 385,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: classItem.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 391,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.level\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 392,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.currentStudents\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 393,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.capacity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 394,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.classTeacherName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium\",\n                                                                                                    children: classItem.classTeacherName\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 398,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: [\n                                                                                                        \"@\",\n                                                                                                        classItem.classTeacherUsername\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 399,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 397,\n                                                                                            columnNumber: 33\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400\",\n                                                                                            children: \"No teacher assigned\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 402,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 395,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 408,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 407,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>handleDelete(\"class\", classItem.id),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 414,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 410,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 406,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 405,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, classItem.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 390,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 388,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"subjects\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Subject Management\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"subject\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 435,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Subject\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Subject Name\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 444,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Code\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 445,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Category\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 446,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Credit Units\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 447,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Teacher\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 448,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 449,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: subject.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 455,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.code\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 456,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.category\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 457,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.creditUnits\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 458,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.teacher\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 459,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 463,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 462,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>handleDelete(\"subject\", subject.id),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 469,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 465,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 461,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 460,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, subject.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 454,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"sessions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Academic Sessions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"session\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Session\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                                            children: sessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border border-gray-200 rounded-lg p-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between items-start mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                                    children: session.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 499,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-3 py-1 text-xs font-semibold rounded-full \".concat(session.status === \"current\" ? \"bg-green-100 text-green-800\" : session.status === \"upcoming\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                                    children: session.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 500,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: \"Start:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 509,\n                                                                                            columnNumber: 30\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        new Date(session.startDate).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 509,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: \"End:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 510,\n                                                                                            columnNumber: 30\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        new Date(session.endDate).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 510,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 514,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 513,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleDelete(\"session\", session.id),\n                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 520,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, session.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"grading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Grading System Configuration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setNewGrade({});\n                                                                        setEditingGradeIndex(-1);\n                                                                        setShowGradeModal(true);\n                                                                    },\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 542,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Grade\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 543,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                            children: \"Grading Type\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: gradingSystem.type,\n                                                                            onChange: (e)=>setGradingSystem({\n                                                                                    ...gradingSystem,\n                                                                                    type: e.target.value\n                                                                                }),\n                                                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"percentage\",\n                                                                                    children: \"Percentage\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 555,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"points\",\n                                                                                    children: \"Points\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 556,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"letter\",\n                                                                                    children: \"Letter Grade\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 557,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                            children: \"Pass Mark (%)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            min: \"0\",\n                                                                            max: \"100\",\n                                                                            value: gradingSystem.passMarkPercentage,\n                                                                            onChange: (e)=>setGradingSystem({\n                                                                                    ...gradingSystem,\n                                                                                    passMarkPercentage: parseInt(e.target.value)\n                                                                                }),\n                                                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Grade\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 577,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Min Score\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 578,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Max Score\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 579,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Points\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 580,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Remark\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 581,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 582,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: (_gradingSystem_grades = gradingSystem.grades) === null || _gradingSystem_grades === void 0 ? void 0 : _gradingSystem_grades.map((grade, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: grade.grade\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 588,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.minScore\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 589,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.maxScore\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 590,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.points\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 591,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.remark\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 592,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>editGrade(index),\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 599,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 595,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>deleteGrade(index),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 605,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 601,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 594,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 593,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 587,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-end pt-6 border-t border-gray-200 mt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSave,\n                                                        disabled: isSaving,\n                                                        className: \"flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors \".concat(isSaving ? \"bg-gray-400 cursor-not-allowed\" : \"bg-primary hover:bg-primary/90\", \" text-white\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SaveIcon, {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isSaving ? \"Saving...\" : \"Save Changes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 361,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 360,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 336,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                        lineNumber: 271,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: [\n                                \"Add New \",\n                                modalType\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 643,\n                            columnNumber: 15\n                        }, this),\n                        modalType === \"class\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Class Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 648,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            placeholder: \"e.g., SS1A\",\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 649,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 647,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 659,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.level || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    level: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS1\",\n                                                    children: \"JSS 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS2\",\n                                                    children: \"JSS 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS3\",\n                                                    children: \"JSS 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS1\",\n                                                    children: \"SS 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS2\",\n                                                    children: \"SS 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS3\",\n                                                    children: \"SS 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 660,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 658,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Class Teacher\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 676,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: teacherSearch,\n                                                    onChange: (e)=>handleTeacherSearch(e.target.value),\n                                                    onFocus: ()=>{\n                                                        console.log(\"Focus - Available teachers:\", teachers.length, teachers);\n                                                        setShowTeacherDropdown(true);\n                                                        if (teacherSearch.trim() === \"\") {\n                                                            setFilteredTeachers(teachers);\n                                                        }\n                                                    },\n                                                    onBlur: ()=>setTimeout(()=>setShowTeacherDropdown(false), 300),\n                                                    placeholder: \"Search by username or name...\",\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 23\n                                                }, this),\n                                                showTeacherDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-[100] w-full mt-1 max-h-48 overflow-y-auto bg-white border-2 border-blue-200 rounded-lg shadow-xl\",\n                                                    children: teachers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 text-center text-red-500\",\n                                                        children: \"No teachers available in database\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 29\n                                                    }, this) : filteredTeachers.length > 0 ? filteredTeachers.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onMouseDown: (e)=>{\n                                                                e.preventDefault();\n                                                                setSelectedTeacher(teacher);\n                                                                setTeacherSearch(\"\");\n                                                                setShowTeacherDropdown(false);\n                                                            },\n                                                            className: \"p-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: teacher.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-blue-600\",\n                                                                    children: [\n                                                                        \"@\",\n                                                                        teacher.username\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, teacher.uid, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 31\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 text-center text-gray-500\",\n                                                        children: teacherSearch ? 'No teachers found matching \"'.concat(teacherSearch, '\"') : \"Start typing to search teachers...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 677,\n                                            columnNumber: 21\n                                        }, this),\n                                        selectedTeacher && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-blue-900\",\n                                                    children: [\n                                                        \"Selected: \",\n                                                        selectedTeacher.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-700\",\n                                                    children: [\n                                                        \"@\",\n                                                        selectedTeacher.username\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 724,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 675,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 646,\n                            columnNumber: 17\n                        }, this),\n                        modalType === \"subject\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Subject Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 736,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 737,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 735,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Subject Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 746,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.code || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    code: e.target.value.toUpperCase()\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 747,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 745,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 756,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.category || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    category: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Core\",\n                                                    children: \"Core\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Science\",\n                                                    children: \"Science\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Arts\",\n                                                    children: \"Arts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Social Science\",\n                                                    children: \"Social Science\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Vocational\",\n                                                    children: \"Vocational\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 757,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 755,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 734,\n                            columnNumber: 17\n                        }, this),\n                        modalType === \"session\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 777,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            placeholder: \"e.g., 2024/2025\",\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 778,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 776,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Start Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 788,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            required: true,\n                                            value: newItem.startDate || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    startDate: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 789,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 787,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"End Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 798,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            required: true,\n                                            value: newItem.endDate || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    endDate: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 799,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 797,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 808,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.status || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    status: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"upcoming\",\n                                                    children: \"Upcoming\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"current\",\n                                                    children: \"Current\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 816,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 809,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 807,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 775,\n                            columnNumber: 17\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddItem,\n                                    disabled: isSaving,\n                                    className: \"flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50\",\n                                    children: isSaving ? \"Creating...\" : \"Create \".concat(modalType)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 824,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddModal(false),\n                                    className: \"flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 831,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 823,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 642,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 641,\n                columnNumber: 7\n            }, this),\n            showGradeModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: editingGradeIndex >= 0 ? \"Edit Grade\" : \"Add New Grade\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 846,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Grade Letter\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 849,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            maxLength: \"2\",\n                                            value: newGrade.grade || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    grade: e.target.value.toUpperCase()\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 850,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 848,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Min Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 860,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    value: newGrade.minScore || \"\",\n                                                    onChange: (e)=>setNewGrade({\n                                                            ...newGrade,\n                                                            minScore: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 859,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Max Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    value: newGrade.maxScore || \"\",\n                                                    onChange: (e)=>setNewGrade({\n                                                            ...newGrade,\n                                                            maxScore: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 870,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 858,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Points\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 883,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"10\",\n                                            value: newGrade.points || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    points: parseInt(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 884,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 882,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Remark\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 894,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: newGrade.remark || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    remark: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 895,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 893,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 847,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: editingGradeIndex >= 0 ? updateGrade : addGrade,\n                                    className: \"flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                    children: editingGradeIndex >= 0 ? \"Update Grade\" : \"Add Grade\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 904,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowGradeModal(false);\n                                        setNewGrade({});\n                                        setEditingGradeIndex(-1);\n                                    },\n                                    className: \"flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 910,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 903,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 845,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 844,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AdminAcademic, \"GW8DprATCrYqLKo1jDj/Go1raV0=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c5 = AdminAcademic;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AcademicCapIcon\");\n$RefreshReg$(_c1, \"PlusIcon\");\n$RefreshReg$(_c2, \"EditIcon\");\n$RefreshReg$(_c3, \"TrashIcon\");\n$RefreshReg$(_c4, \"SaveIcon\");\n$RefreshReg$(_c5, \"AdminAcademic\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard/admin/academic.js\n"));

/***/ })

});