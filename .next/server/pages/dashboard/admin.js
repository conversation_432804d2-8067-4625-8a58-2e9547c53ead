/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/dashboard/admin";
exports.ids = ["pages/dashboard/admin"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\dashboard\\admin.js */ \"./pages/dashboard/admin.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/dashboard/admin\",\n        pathname: \"/dashboard/admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/DashboardLayout.js":
/*!***************************************!*\
  !*** ./components/DashboardLayout.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { userProfile, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const user = userProfile || {\n        name: \"Loading...\",\n        role: \"admin\"\n    };\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n            router.push(\"/login\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            router.push(\"/login\");\n        }\n    };\n    // Navigation items based on user role\n    // Professional SVG Icons for Navigation\n    const DashboardIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 28,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 29,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 27,\n            columnNumber: 5\n        }, this);\n    const UsersIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, this);\n    const CogIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 41,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 42,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, this);\n    const AcademicIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 14l9-5-9-5-9 5 9 5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 48,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 49,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 47,\n            columnNumber: 5\n        }, this);\n    const ChartIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 54,\n            columnNumber: 5\n        }, this);\n    const CalendarIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 60,\n            columnNumber: 5\n        }, this);\n    const DocumentIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 66,\n            columnNumber: 5\n        }, this);\n    const navigationItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard/admin\",\n            icon: DashboardIcon\n        },\n        {\n            name: \"User Management\",\n            href: \"/dashboard/admin/users\",\n            icon: UsersIcon\n        },\n        {\n            name: \"Academic Setup\",\n            href: \"/dashboard/admin/academic\",\n            icon: AcademicIcon\n        },\n        {\n            name: \"Reports\",\n            href: \"/dashboard/admin/reports\",\n            icon: ChartIcon\n        },\n        {\n            name: \"Settings\",\n            href: \"/dashboard/admin/settings\",\n            icon: CogIcon\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-16 px-4 border-b border-gray-200 flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: \"/GHC.jpg\",\n                                    alt: \"Great Heritage College\",\n                                    width: 36,\n                                    height: 36,\n                                    className: \"rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary font-bold text-lg block\",\n                                            children: \"GHC Portal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 capitalize\",\n                                            children: [\n                                                user?.role,\n                                                \" Panel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 px-4 py-6 overflow-y-auto\",\n                        children: navigationItems.map((item)=>{\n                            const IconComponent = item.icon;\n                            const isActive = router.pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: item.href,\n                                className: `flex items-center px-4 py-3 mb-2 rounded-lg transition-all duration-200 ${isActive ? \"bg-primary text-white shadow-md\" : \"text-gray-700 hover:bg-gray-100 hover:text-primary\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-3 font-medium\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, item.name, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white border-b border-gray-200 shadow-sm flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                            className: \"lg:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        \"Good \",\n                                                        new Date().getHours() < 12 ? \"Morning\" : new Date().getHours() < 18 ? \"Afternoon\" : \"Evening\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        user.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12M1.01 11.88l3.12 3.12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 px-3 py-2 rounded-lg bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-primary rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-semibold text-sm\",\n                                                        children: user.name.charAt(0).toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 capitalize\",\n                                                            children: user.role\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"p-2 rounded-lg text-gray-600 hover:text-red-600 hover:bg-red-50 transition-colors\",\n                                            title: \"Logout\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-6 bg-gray-50 overflow-y-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0Rhc2hib2FyZExheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWdDO0FBQ087QUFDWDtBQUNFO0FBQ21CO0FBRWxDLFNBQVNLLGdCQUFnQixFQUFFQyxRQUFRLEVBQUU7SUFDbEQsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdSLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU1TLFNBQVNSLHNEQUFTQTtJQUV4QixNQUFNLEVBQUVTLFdBQVcsRUFBRUMsTUFBTSxFQUFFLEdBQUdQLDhEQUFPQTtJQUN2QyxNQUFNUSxPQUFPRixlQUFlO1FBQUVHLE1BQU07UUFBY0MsTUFBTTtJQUFRO0lBRWhFLE1BQU1DLGVBQWU7UUFDbkIsSUFBSTtZQUNGLE1BQU1KO1lBQ05GLE9BQU9PLElBQUksQ0FBQztRQUNkLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUJBQWlCQTtZQUMvQlIsT0FBT08sSUFBSSxDQUFDO1FBQ2Q7SUFDRjtJQUVBLHNDQUFzQztJQUN0Qyx3Q0FBd0M7SUFDeEMsTUFBTUcsZ0JBQWdCLGtCQUNwQiw4REFBQ0M7WUFBSUMsV0FBVTtZQUFVQyxNQUFLO1lBQU9DLFFBQU87WUFBZUMsU0FBUTs7OEJBQ2pFLDhEQUFDQztvQkFBS0MsZUFBYztvQkFBUUMsZ0JBQWU7b0JBQVFDLGFBQWE7b0JBQUdDLEdBQUU7Ozs7Ozs4QkFDckUsOERBQUNKO29CQUFLQyxlQUFjO29CQUFRQyxnQkFBZTtvQkFBUUMsYUFBYTtvQkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7O0lBSXpFLE1BQU1DLFlBQVksa0JBQ2hCLDhEQUFDVjtZQUFJQyxXQUFVO1lBQVVDLE1BQUs7WUFBT0MsUUFBTztZQUFlQyxTQUFRO3NCQUNqRSw0RUFBQ0M7Z0JBQUtDLGVBQWM7Z0JBQVFDLGdCQUFlO2dCQUFRQyxhQUFhO2dCQUFHQyxHQUFFOzs7Ozs7Ozs7OztJQUl6RSxNQUFNRSxVQUFVLGtCQUNkLDhEQUFDWDtZQUFJQyxXQUFVO1lBQVVDLE1BQUs7WUFBT0MsUUFBTztZQUFlQyxTQUFROzs4QkFDakUsOERBQUNDO29CQUFLQyxlQUFjO29CQUFRQyxnQkFBZTtvQkFBUUMsYUFBYTtvQkFBR0MsR0FBRTs7Ozs7OzhCQUNyRSw4REFBQ0o7b0JBQUtDLGVBQWM7b0JBQVFDLGdCQUFlO29CQUFRQyxhQUFhO29CQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7SUFJekUsTUFBTUcsZUFBZSxrQkFDbkIsOERBQUNaO1lBQUlDLFdBQVU7WUFBVUMsTUFBSztZQUFPQyxRQUFPO1lBQWVDLFNBQVE7OzhCQUNqRSw4REFBQ0M7b0JBQUtDLGVBQWM7b0JBQVFDLGdCQUFlO29CQUFRQyxhQUFhO29CQUFHQyxHQUFFOzs7Ozs7OEJBQ3JFLDhEQUFDSjtvQkFBS0MsZUFBYztvQkFBUUMsZ0JBQWU7b0JBQVFDLGFBQWE7b0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7OztJQUl6RSxNQUFNSSxZQUFZLGtCQUNoQiw4REFBQ2I7WUFBSUMsV0FBVTtZQUFVQyxNQUFLO1lBQU9DLFFBQU87WUFBZUMsU0FBUTtzQkFDakUsNEVBQUNDO2dCQUFLQyxlQUFjO2dCQUFRQyxnQkFBZTtnQkFBUUMsYUFBYTtnQkFBR0MsR0FBRTs7Ozs7Ozs7Ozs7SUFJekUsTUFBTUssZUFBZSxrQkFDbkIsOERBQUNkO1lBQUlDLFdBQVU7WUFBVUMsTUFBSztZQUFPQyxRQUFPO1lBQWVDLFNBQVE7c0JBQ2pFLDRFQUFDQztnQkFBS0MsZUFBYztnQkFBUUMsZ0JBQWU7Z0JBQVFDLGFBQWE7Z0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7O0lBSXpFLE1BQU1NLGVBQWUsa0JBQ25CLDhEQUFDZjtZQUFJQyxXQUFVO1lBQVVDLE1BQUs7WUFBT0MsUUFBTztZQUFlQyxTQUFRO3NCQUNqRSw0RUFBQ0M7Z0JBQUtDLGVBQWM7Z0JBQVFDLGdCQUFlO2dCQUFRQyxhQUFhO2dCQUFHQyxHQUFFOzs7Ozs7Ozs7OztJQUl6RSxNQUFNTyxrQkFBa0I7UUFDdEI7WUFBRXZCLE1BQU07WUFBYXdCLE1BQU07WUFBb0JDLE1BQU1uQjtRQUFjO1FBQ25FO1lBQUVOLE1BQU07WUFBbUJ3QixNQUFNO1lBQTBCQyxNQUFNUjtRQUFVO1FBQzNFO1lBQUVqQixNQUFNO1lBQWtCd0IsTUFBTTtZQUE2QkMsTUFBTU47UUFBYTtRQUNoRjtZQUFFbkIsTUFBTTtZQUFXd0IsTUFBTTtZQUE0QkMsTUFBTUw7UUFBVTtRQUNyRTtZQUFFcEIsTUFBTTtZQUFZd0IsTUFBTTtZQUE2QkMsTUFBTVA7UUFBUTtLQUN0RTtJQUVELHFCQUNFLDhEQUFDUTtRQUFJbEIsV0FBVTs7WUFFWmQsNkJBQ0MsOERBQUNnQztnQkFDQ2xCLFdBQVU7Z0JBQ1ZtQixTQUFTLElBQU1oQyxlQUFlOzs7Ozs7MEJBS2xDLDhEQUFDK0I7Z0JBQUlsQixXQUFXLENBQUMsNEtBQTRLLEVBQzNMZCxjQUFjLGtCQUFrQixvQkFDakMsQ0FBQzs7a0NBQ0EsOERBQUNnQzt3QkFBSWxCLFdBQVU7a0NBQ2IsNEVBQUNuQixrREFBSUE7NEJBQUNtQyxNQUFLOzRCQUFJaEIsV0FBVTs7OENBQ3ZCLDhEQUFDbEIsbURBQUtBO29DQUNKc0MsS0FBSTtvQ0FDSkMsS0FBSTtvQ0FDSkMsT0FBTztvQ0FDUEMsUUFBUTtvQ0FDUnZCLFdBQVU7Ozs7Ozs4Q0FFWiw4REFBQ2tCO29DQUFJbEIsV0FBVTs7c0RBQ2IsOERBQUN3Qjs0Q0FBS3hCLFdBQVU7c0RBQXVDOzs7Ozs7c0RBQ3ZELDhEQUFDd0I7NENBQUt4QixXQUFVOztnREFBb0NULE1BQU1FO2dEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3JFLDhEQUFDZ0M7d0JBQUl6QixXQUFVO2tDQUNaZSxnQkFBZ0JXLEdBQUcsQ0FBQyxDQUFDQzs0QkFDcEIsTUFBTUMsZ0JBQWdCRCxLQUFLVixJQUFJOzRCQUMvQixNQUFNWSxXQUFXekMsT0FBTzBDLFFBQVEsS0FBS0gsS0FBS1gsSUFBSTs0QkFDOUMscUJBQ0UsOERBQUNuQyxrREFBSUE7Z0NBRUhtQyxNQUFNVyxLQUFLWCxJQUFJO2dDQUNmaEIsV0FBVyxDQUFDLHdFQUF3RSxFQUNsRjZCLFdBQ0ksb0NBQ0EscURBQ0wsQ0FBQzs7a0RBRUYsOERBQUNEOzs7OztrREFDRCw4REFBQ0o7d0NBQUt4QixXQUFVO2tEQUFvQjJCLEtBQUtuQyxJQUFJOzs7Ozs7OytCQVR4Q21DLEtBQUtuQyxJQUFJOzs7Ozt3QkFZcEI7Ozs7Ozs7Ozs7OzswQkFLSiw4REFBQzBCO2dCQUFJbEIsV0FBVTs7a0NBRWIsOERBQUMrQjt3QkFBTy9CLFdBQVU7a0NBQ2hCLDRFQUFDa0I7NEJBQUlsQixXQUFVOzs4Q0FDYiw4REFBQ2tCO29DQUFJbEIsV0FBVTs7c0RBQ2IsOERBQUNnQzs0Q0FDQ2IsU0FBUyxJQUFNaEMsZUFBZSxDQUFDRDs0Q0FDL0JjLFdBQVU7c0RBRVYsNEVBQUNEO2dEQUFJQyxXQUFVO2dEQUFVQyxNQUFLO2dEQUFPQyxRQUFPO2dEQUFlQyxTQUFROzBEQUNqRSw0RUFBQ0M7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFhO29EQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUd6RSw4REFBQ1U7NENBQUlsQixXQUFVOzs4REFDYiw4REFBQ2lDO29EQUFHakMsV0FBVTs7d0RBQW1DO3dEQUN6QyxJQUFJa0MsT0FBT0MsUUFBUSxLQUFLLEtBQUssWUFBWSxJQUFJRCxPQUFPQyxRQUFRLEtBQUssS0FBSyxjQUFjOzs7Ozs7OzhEQUU1Riw4REFBQ0M7b0RBQUVwQyxXQUFVOzt3REFBd0I7d0RBQWVULEtBQUtDLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSWpFLDhEQUFDMEI7b0NBQUlsQixXQUFVOztzREFFYiw4REFBQ2dDOzRDQUFPaEMsV0FBVTs7OERBQ2hCLDhEQUFDRDtvREFBSUMsV0FBVTtvREFBVUMsTUFBSztvREFBT0MsUUFBTztvREFBZUMsU0FBUTs4REFDakUsNEVBQUNDO3dEQUFLQyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUUMsYUFBYTt3REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7OERBRXZFLDhEQUFDZ0I7b0RBQUt4QixXQUFVOzs7Ozs7Ozs7Ozs7c0RBSWxCLDhEQUFDa0I7NENBQUlsQixXQUFVOzs4REFDYiw4REFBQ2tCO29EQUFJbEIsV0FBVTs4REFDYiw0RUFBQ3dCO3dEQUFLeEIsV0FBVTtrRUFDYlQsS0FBS0MsSUFBSSxDQUFDNkMsTUFBTSxDQUFDLEdBQUdDLFdBQVc7Ozs7Ozs7Ozs7OzhEQUdwQyw4REFBQ3BCO29EQUFJbEIsV0FBVTs7c0VBQ2IsOERBQUNvQzs0REFBRXBDLFdBQVU7c0VBQXFDVCxLQUFLQyxJQUFJOzs7Ozs7c0VBQzNELDhEQUFDNEM7NERBQUVwQyxXQUFVO3NFQUFvQ1QsS0FBS0UsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUk5RCw4REFBQ3VDOzRDQUNDYixTQUFTekI7NENBQ1RNLFdBQVU7NENBQ1Z1QyxPQUFNO3NEQUVOLDRFQUFDeEM7Z0RBQUlDLFdBQVU7Z0RBQVVDLE1BQUs7Z0RBQU9DLFFBQU87Z0RBQWVDLFNBQVE7MERBQ2pFLDRFQUFDQztvREFBS0MsZUFBYztvREFBUUMsZ0JBQWU7b0RBQVFDLGFBQWE7b0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FRL0UsOERBQUNnQzt3QkFBS3hDLFdBQVU7a0NBQ2JmOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL2dyZWF0LWhlcml0YWdlLWNvbGxlZ2UtZnJvbnRlbmQvLi9jb21wb25lbnRzL0Rhc2hib2FyZExheW91dC5qcz8xMzczIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcidcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJ1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJy4uL2NvbnRleHRzL0F1dGhDb250ZXh0J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmRMYXlvdXQoeyBjaGlsZHJlbiB9KSB7XG4gIGNvbnN0IFtzaWRlYmFyT3Blbiwgc2V0U2lkZWJhck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIFxuICBjb25zdCB7IHVzZXJQcm9maWxlLCBsb2dvdXQgfSA9IHVzZUF1dGgoKVxuICBjb25zdCB1c2VyID0gdXNlclByb2ZpbGUgfHwgeyBuYW1lOiAnTG9hZGluZy4uLicsIHJvbGU6ICdhZG1pbicgfVxuICBcbiAgY29uc3QgaGFuZGxlTG9nb3V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBsb2dvdXQoKVxuICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0xvZ291dCBlcnJvcjonLCBlcnJvcilcbiAgICAgIHJvdXRlci5wdXNoKCcvbG9naW4nKVxuICAgIH1cbiAgfVxuXG4gIC8vIE5hdmlnYXRpb24gaXRlbXMgYmFzZWQgb24gdXNlciByb2xlXG4gIC8vIFByb2Zlc3Npb25hbCBTVkcgSWNvbnMgZm9yIE5hdmlnYXRpb25cbiAgY29uc3QgRGFzaGJvYXJkSWNvbiA9ICgpID0+IChcbiAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0zIDd2MTBhMiAyIDAgMDAyIDJoMTRhMiAyIDAgMDAyLTJWOWEyIDIgMCAwMC0yLTJINWEyIDIgMCAwMC0yLTJ6XCIgLz5cbiAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk04IDVhMiAyIDAgMDEyLTJoNGEyIDIgMCAwMTIgMnY2SDhWNXpcIiAvPlxuICAgIDwvc3ZnPlxuICApXG5cbiAgY29uc3QgVXNlcnNJY29uID0gKCkgPT4gKFxuICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDQuMzU0YTQgNCAwIDExMCA1LjI5Mk0xNSAyMUgzdi0xYTYgNiAwIDAxMTIgMHYxem0wIDBoNnYtMWE2IDYgMCAwMC05LTUuMTk3bTEzLjUtOWEyLjUgMi41IDAgMTEtNSAwIDIuNSAyLjUgMCAwMTUgMHpcIiAvPlxuICAgIDwvc3ZnPlxuICApXG5cbiAgY29uc3QgQ29nSWNvbiA9ICgpID0+IChcbiAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMC4zMjUgNC4zMTdjLjQyNi0xLjc1NiAyLjkyNC0xLjc1NiAzLjM1IDBhMS43MjQgMS43MjQgMCAwMDIuNTczIDEuMDY2YzEuNTQzLS45NCAzLjMxLjgyNiAyLjM3IDIuMzdhMS43MjQgMS43MjQgMCAwMDEuMDY1IDIuNTcyYzEuNzU2LjQyNiAxLjc1NiAyLjkyNCAwIDMuMzVhMS43MjQgMS43MjQgMCAwMC0xLjA2NiAyLjU3M2MuOTQgMS41NDMtLjgyNiAzLjMxLTIuMzcgMi4zN2ExLjcyNCAxLjcyNCAwIDAwLTIuNTcyIDEuMDY1Yy0uNDI2IDEuNzU2LTIuOTI0IDEuNzU2LTMuMzUgMGExLjcyNCAxLjcyNCAwIDAwLTIuNTczLTEuMDY2Yy0xLjU0My45NC0zLjMxLS44MjYtMi4zNy0yLjM3YTEuNzI0IDEuNzI0IDAgMDAtMS4wNjUtMi41NzJjLTEuNzU2LS40MjYtMS43NTYtMi45MjQgMC0zLjM1YTEuNzI0IDEuNzI0IDAgMDAxLjA2Ni0yLjU3M2MtLjk0LTEuNTQzLjgyNi0zLjMxIDIuMzctMi4zNy45OTYuNjA4IDIuMjk2LjA3IDIuNTcyLTEuMDY1elwiIC8+XG4gICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTUgMTJhMyAzIDAgMTEtNiAwIDMgMyAwIDAxNiAwelwiIC8+XG4gICAgPC9zdmc+XG4gIClcblxuICBjb25zdCBBY2FkZW1pY0ljb24gPSAoKSA9PiAoXG4gICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTIgMTRsOS01LTktNS05IDUgOSA1elwiIC8+XG4gICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTIgMTRsNi4xNi0zLjQyMmExMi4wODMgMTIuMDgzIDAgMDEuNjY1IDYuNDc5QTExLjk1MiAxMS45NTIgMCAwMDEyIDIwLjA1NWExMS45NTIgMTEuOTUyIDAgMDAtNi44MjQtMi45OTggMTIuMDc4IDEyLjA3OCAwIDAxLjY2NS02LjQ3OUwxMiAxNHpcIiAvPlxuICAgIDwvc3ZnPlxuICApXG5cbiAgY29uc3QgQ2hhcnRJY29uID0gKCkgPT4gKFxuICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTkgMTl2LTZhMiAyIDAgMDAtMi0ySDVhMiAyIDAgMDAtMiAydjZhMiAyIDAgMDAyIDJoMmEyIDIgMCAwMDItMnptMCAwVjlhMiAyIDAgMDEyLTJoMmEyIDIgMCAwMTIgMnYxMG0tNiAwYTIgMiAwIDAwMiAyaDJhMiAyIDAgMDAyLTJtMCAwVjVhMiAyIDAgMDEyLTJoMmEyIDIgMCAwMTIgMnYxNGEyIDIgMCAwMS0yIDJoLTJhMiAyIDAgMDEtMi0yelwiIC8+XG4gICAgPC9zdmc+XG4gIClcblxuICBjb25zdCBDYWxlbmRhckljb24gPSAoKSA9PiAoXG4gICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNOCA3VjNtOCA0VjNtLTkgOGgxME01IDIxaDE0YTIgMiAwIDAwMi0yVjdhMiAyIDAgMDAtMi0ySDVhMiAyIDAgMDAtMiAydjEyYTIgMiAwIDAwMiAyelwiIC8+XG4gICAgPC9zdmc+XG4gIClcblxuICBjb25zdCBEb2N1bWVudEljb24gPSAoKSA9PiAoXG4gICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNOSAxMmg2bS02IDRoNm0yIDVIN2EyIDIgMCAwMS0yLTJWNWEyIDIgMCAwMTItMmg1LjU4NmExIDEgMCAwMS43MDcuMjkzbDUuNDE0IDUuNDE0YTEgMSAwIDAxLjI5My43MDdWMTlhMiAyIDAgMDEtMiAyelwiIC8+XG4gICAgPC9zdmc+XG4gIClcblxuICBjb25zdCBuYXZpZ2F0aW9uSXRlbXMgPSBbXG4gICAgeyBuYW1lOiAnRGFzaGJvYXJkJywgaHJlZjogJy9kYXNoYm9hcmQvYWRtaW4nLCBpY29uOiBEYXNoYm9hcmRJY29uIH0sXG4gICAgeyBuYW1lOiAnVXNlciBNYW5hZ2VtZW50JywgaHJlZjogJy9kYXNoYm9hcmQvYWRtaW4vdXNlcnMnLCBpY29uOiBVc2Vyc0ljb24gfSxcbiAgICB7IG5hbWU6ICdBY2FkZW1pYyBTZXR1cCcsIGhyZWY6ICcvZGFzaGJvYXJkL2FkbWluL2FjYWRlbWljJywgaWNvbjogQWNhZGVtaWNJY29uIH0sXG4gICAgeyBuYW1lOiAnUmVwb3J0cycsIGhyZWY6ICcvZGFzaGJvYXJkL2FkbWluL3JlcG9ydHMnLCBpY29uOiBDaGFydEljb24gfSxcbiAgICB7IG5hbWU6ICdTZXR0aW5ncycsIGhyZWY6ICcvZGFzaGJvYXJkL2FkbWluL3NldHRpbmdzJywgaWNvbjogQ29nSWNvbiB9XG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleFwiPlxuICAgICAgey8qIE1vYmlsZSBzaWRlYmFyIGJhY2tkcm9wICovfVxuICAgICAge3NpZGViYXJPcGVuICYmIChcbiAgICAgICAgPGRpdlxuICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei00MCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGxnOmhpZGRlblwiXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2lkZWJhck9wZW4oZmFsc2UpfVxuICAgICAgICAvPlxuICAgICAgKX1cblxuICAgICAgey8qIFNpZGViYXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YGZpeGVkIGluc2V0LXktMCBsZWZ0LTAgei01MCB3LTY0IGJnLXdoaXRlIGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMCB0cmFuc2Zvcm0gdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0IGxnOnRyYW5zbGF0ZS14LTAgbGc6cmVsYXRpdmUgbGc6ZmxleCBsZzpmbGV4LWNvbCAke1xuICAgICAgICBzaWRlYmFyT3BlbiA/ICd0cmFuc2xhdGUteC0wJyA6ICctdHJhbnNsYXRlLXgtZnVsbCdcbiAgICAgIH1gfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTE2IHB4LTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgIHNyYz1cIi9HSEMuanBnXCJcbiAgICAgICAgICAgICAgYWx0PVwiR3JlYXQgSGVyaXRhZ2UgQ29sbGVnZVwiXG4gICAgICAgICAgICAgIHdpZHRoPXszNn1cbiAgICAgICAgICAgICAgaGVpZ2h0PXszNn1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC1mdWxsXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGVmdFwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnkgZm9udC1ib2xkIHRleHQtbGcgYmxvY2tcIj5HSEMgUG9ydGFsPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgY2FwaXRhbGl6ZVwiPnt1c2VyPy5yb2xlfSBQYW5lbDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4LTEgcHgtNCBweS02IG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgIHtuYXZpZ2F0aW9uSXRlbXMubWFwKChpdGVtKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBJY29uQ29tcG9uZW50ID0gaXRlbS5pY29uXG4gICAgICAgICAgICBjb25zdCBpc0FjdGl2ZSA9IHJvdXRlci5wYXRobmFtZSA9PT0gaXRlbS5ocmVmXG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGtleT17aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTMgbWItMiByb3VuZGVkLWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICAgICAgaXNBY3RpdmVcbiAgICAgICAgICAgICAgICAgICAgPyAnYmctcHJpbWFyeSB0ZXh0LXdoaXRlIHNoYWRvdy1tZCdcbiAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMCBob3Zlcjp0ZXh0LXByaW1hcnknXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8SWNvbkNvbXBvbmVudCAvPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTMgZm9udC1tZWRpdW1cIj57aXRlbS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgKVxuICAgICAgICAgIH0pfVxuICAgICAgICA8L25hdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTWFpbiBjb250ZW50ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBtaW4taC1zY3JlZW5cIj5cbiAgICAgICAgey8qIFRvcCBuYXZpZ2F0aW9uICovfVxuICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBzaGFkb3ctc20gZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB4LTYgcHktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2lkZWJhck9wZW4oIXNpZGViYXJPcGVuKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJsZzpoaWRkZW4gcC0yIHJvdW5kZWQtbGcgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGhvdmVyOmJnLWdyYXktMTAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy02IGgtNlwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTQgNmgxNk00IDEyaDE2TTQgMThoMTZcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC00XCI+XG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICBHb29kIHtuZXcgRGF0ZSgpLmdldEhvdXJzKCkgPCAxMiA/ICdNb3JuaW5nJyA6IG5ldyBEYXRlKCkuZ2V0SG91cnMoKSA8IDE4ID8gJ0FmdGVybm9vbicgOiAnRXZlbmluZyd9XG4gICAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5XZWxjb21lIGJhY2ssIHt1c2VyLm5hbWV9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICB7LyogTm90aWZpY2F0aW9ucyAqL31cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTIgcm91bmRlZC1sZyB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnMgcmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTZcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNSAxN2g1bC01IDV2LTV6TTEwLjA3IDIuODJsMy4xMiAzLjEyTTcuMDUgNS44NGwzLjEyIDMuMTJNNC4wMyA4Ljg2bDMuMTIgMy4xMk0xLjAxIDExLjg4bDMuMTIgMy4xMlwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTEgcmlnaHQtMSB3LTIgaC0yIGJnLXJlZC01MDAgcm91bmRlZC1mdWxsXCI+PC9zcGFuPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICB7LyogVXNlciBNZW51ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBweC0zIHB5LTIgcm91bmRlZC1sZyBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLXByaW1hcnkgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICB7dXNlci5uYW1lLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e3VzZXIubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgY2FwaXRhbGl6ZVwiPnt1c2VyLnJvbGV9PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTG9nb3V0fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWxnIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1yZWQtNjAwIGhvdmVyOmJnLXJlZC01MCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJMb2dvdXRcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTcgMTZsNC00bTAgMGwtNC00bTQgNEg3bTYgNHYxYTMgMyAwIDAxLTMgM0g2YTMgMyAwIDAxLTMtM1Y3YTMgMyAwIDAxMy0zaDRhMyAzIDAgMDEzIDN2MVwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvaGVhZGVyPlxuXG4gICAgICAgIHsvKiBQYWdlIGNvbnRlbnQgKi99XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBwLTYgYmctZ3JheS01MCBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvbWFpbj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VSb3V0ZXIiLCJMaW5rIiwiSW1hZ2UiLCJ1c2VBdXRoIiwiRGFzaGJvYXJkTGF5b3V0IiwiY2hpbGRyZW4iLCJzaWRlYmFyT3BlbiIsInNldFNpZGViYXJPcGVuIiwicm91dGVyIiwidXNlclByb2ZpbGUiLCJsb2dvdXQiLCJ1c2VyIiwibmFtZSIsInJvbGUiLCJoYW5kbGVMb2dvdXQiLCJwdXNoIiwiZXJyb3IiLCJjb25zb2xlIiwiRGFzaGJvYXJkSWNvbiIsInN2ZyIsImNsYXNzTmFtZSIsImZpbGwiLCJzdHJva2UiLCJ2aWV3Qm94IiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsIlVzZXJzSWNvbiIsIkNvZ0ljb24iLCJBY2FkZW1pY0ljb24iLCJDaGFydEljb24iLCJDYWxlbmRhckljb24iLCJEb2N1bWVudEljb24iLCJuYXZpZ2F0aW9uSXRlbXMiLCJocmVmIiwiaWNvbiIsImRpdiIsIm9uQ2xpY2siLCJzcmMiLCJhbHQiLCJ3aWR0aCIsImhlaWdodCIsInNwYW4iLCJuYXYiLCJtYXAiLCJpdGVtIiwiSWNvbkNvbXBvbmVudCIsImlzQWN0aXZlIiwicGF0aG5hbWUiLCJoZWFkZXIiLCJidXR0b24iLCJoMSIsIkRhdGUiLCJnZXRIb3VycyIsInAiLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsInRpdGxlIiwibWFpbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/DashboardLayout.js\n");

/***/ }),

/***/ "./components/ProtectedRoute.js":
/*!**************************************!*\
  !*** ./components/ProtectedRoute.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction ProtectedRoute({ children, allowedRoles = [] }) {\n    const { currentUser, userProfile, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!loading) {\n            if (!currentUser) {\n                setIsRedirecting(true);\n                router.push(`/login?from=${encodeURIComponent(router.asPath)}`);\n                return;\n            }\n            if (!userProfile) {\n                setIsRedirecting(true);\n                router.push(`/login?from=${encodeURIComponent(router.asPath)}`);\n                return;\n            }\n            // Role-based check\n            if (allowedRoles.length > 0 && !allowedRoles.includes(userProfile.role)) {\n                setIsRedirecting(true);\n                router.push(\"/403\");\n                return;\n            }\n            // If we get here, user is authorized\n            setIsRedirecting(false);\n        }\n    }, [\n        currentUser,\n        userProfile,\n        loading,\n        router,\n        allowedRoles\n    ]);\n    // Show loading spinner\n    if (loading || isRedirecting) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-primary/5 via-white to-accent/5 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        src: \"/GHC.jpg\",\n                        alt: \"Great Heritage College\",\n                        width: 80,\n                        height: 80,\n                        className: \"rounded-full mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\ProtectedRoute.js\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\ProtectedRoute.js\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: loading ? \"Loading...\" : \"Redirecting...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\ProtectedRoute.js\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\ProtectedRoute.js\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\ProtectedRoute.js\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if user is not authenticated\n    if (!currentUser || !userProfile) {\n        return null;\n    }\n    // Don't render if user doesn't have required role\n    if (allowedRoles.length > 0 && !allowedRoles.includes(userProfile.role)) {\n        return null;\n    }\n    // Render children if all checks pass\n    return children;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ProtectedRoute.js\n");

/***/ }),

/***/ "./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/firebase */ \"./lib/firebase.js\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authService */ \"./lib/authService.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase__WEBPACK_IMPORTED_MODULE_3__, _lib_authService__WEBPACK_IMPORTED_MODULE_4__]);\n([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase__WEBPACK_IMPORTED_MODULE_3__, _lib_authService__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    currentUser: null,\n    userProfile: null,\n    loading: true,\n    isAdmin: false,\n    isProprietor: false,\n    isTeacher: false,\n    isStudent: false,\n    isParent: false,\n    hasRole: ()=>false,\n    login: async ()=>{},\n    logout: async ()=>{},\n    refreshUserProfile: async ()=>{}\n});\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\nfunction AuthProvider({ children }) {\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, async (user)=>{\n            setCurrentUser(user);\n            if (user) {\n                try {\n                    const profileData = await (0,_lib_authService__WEBPACK_IMPORTED_MODULE_4__.getUserProfile)(user.uid);\n                    if (profileData) {\n                        console.log(\"User profile loaded:\", {\n                            uid: user.uid,\n                            username: profileData.username,\n                            role: profileData.role,\n                            name: profileData.name\n                        });\n                        setUserProfile(profileData);\n                    } else {\n                        console.warn(\"No profile found for user:\", user.uid);\n                        setUserProfile(null);\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching user profile:\", error);\n                    setUserProfile(null);\n                }\n            } else {\n                setUserProfile(null);\n            }\n            setLoading(false);\n        });\n        return unsubscribe;\n    }, []);\n    // Role helpers\n    const isAdmin = userProfile?.role === \"admin\";\n    const isProprietor = userProfile?.role === \"proprietor\";\n    const isTeacher = userProfile?.role === \"teacher\";\n    const isStudent = userProfile?.role === \"student\";\n    const isParent = userProfile?.role === \"parent\";\n    const hasRole = (roles)=>{\n        if (!userProfile) return false;\n        return Array.isArray(roles) ? roles.includes(userProfile.role) : userProfile.role === roles;\n    };\n    const login = async (username, password)=>{\n        return (0,_lib_authService__WEBPACK_IMPORTED_MODULE_4__.loginWithUsername)(username, password);\n    };\n    const logout = async ()=>{\n        return (0,_lib_authService__WEBPACK_IMPORTED_MODULE_4__.logoutUser)();\n    };\n    const refreshUserProfile = async ()=>{\n        if (!currentUser) return;\n        try {\n            const profileData = await (0,_lib_authService__WEBPACK_IMPORTED_MODULE_4__.getUserProfile)(currentUser.uid);\n            setUserProfile(profileData);\n            console.log(\"User profile refreshed:\", profileData?.role);\n        } catch (error) {\n            console.error(\"Error refreshing user profile:\", error);\n        }\n    };\n    const value = {\n        currentUser,\n        userProfile,\n        loading,\n        isAdmin,\n        isProprietor,\n        isTeacher,\n        isStudent,\n        isParent,\n        hasRole,\n        login,\n        logout,\n        refreshUserProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/AuthContext.js\n");

/***/ }),

/***/ "./lib/authService.js":
/*!****************************!*\
  !*** ./lib/authService.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES),\n/* harmony export */   changeUserRole: () => (/* binding */ changeUserRole),\n/* harmony export */   checkUsernameAvailability: () => (/* binding */ checkUsernameAvailability),\n/* harmony export */   createAdminUser: () => (/* binding */ createAdminUser),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUsersByRole: () => (/* binding */ getUsersByRole),\n/* harmony export */   loginWithUsername: () => (/* binding */ loginWithUsername),\n/* harmony export */   logoutUser: () => (/* binding */ logoutUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   resetPassword: () => (/* binding */ resetPassword),\n/* harmony export */   updateUserProfile: () => (/* binding */ updateUserProfile)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firebase */ \"./lib/firebase.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_auth__WEBPACK_IMPORTED_MODULE_0__, firebase_firestore__WEBPACK_IMPORTED_MODULE_1__, _firebase__WEBPACK_IMPORTED_MODULE_2__]);\n([firebase_auth__WEBPACK_IMPORTED_MODULE_0__, firebase_firestore__WEBPACK_IMPORTED_MODULE_1__, _firebase__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n/**\n * School roles\n */ const USER_ROLES = {\n    ADMIN: \"admin\",\n    PROPRIETOR: \"proprietor\",\n    TEACHER: \"teacher\",\n    STUDENT: \"student\",\n    PARENT: \"parent\"\n};\n/**\n * Login with username instead of email\n */ const loginWithUsername = async (username, password)=>{\n    try {\n        // First, get the email associated with this username\n        const usernameDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()));\n        if (!usernameDoc.exists()) {\n            throw new Error(\"Username not found\");\n        }\n        const { email } = usernameDoc.data();\n        // Now login with email and password\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email, password);\n        return userCredential.user;\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        throw error;\n    }\n};\n/**\n * Register a new user with username\n */ const registerUser = async (username, email, password, role = USER_ROLES.STUDENT, profileData = {})=>{\n    try {\n        // Check if username already exists\n        const usernameDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()));\n        if (usernameDoc.exists()) {\n            throw new Error(\"Username already exists\");\n        }\n        // Auto-generate email for students if not provided\n        let finalEmail = email;\n        if (!email && role === USER_ROLES.STUDENT) {\n            finalEmail = `${username.toLowerCase()}@student.greatheritagecollege.edu`;\n        }\n        if (!finalEmail) {\n            throw new Error(\"Email is required for non-student users\");\n        }\n        // Create user in Firebase Auth\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, finalEmail, password);\n        const user = userCredential.user;\n        // Set display name\n        const displayName = profileData.name || username;\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.updateProfile)(user, {\n            displayName\n        });\n        // Create user document in Firestore\n        const userData = {\n            uid: user.uid,\n            username: username.toLowerCase(),\n            email: user.email,\n            name: displayName,\n            role,\n            isActive: true,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)(),\n            ...profileData\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", user.uid), userData);\n        // Create username mapping\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()), {\n            uid: user.uid,\n            email: user.email,\n            role\n        });\n        return userData;\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        throw error;\n    }\n};\n/**\n * Logout user\n */ const logoutUser = async ()=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n    } catch (error) {\n        console.error(\"Logout error:\", error);\n        throw error;\n    }\n};\n/**\n * Get user profile from Firestore\n */ const getUserProfile = async (userId)=>{\n    try {\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", userId));\n        if (userDoc.exists()) {\n            return userDoc.data();\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error getting user profile:\", error);\n        throw error;\n    }\n};\n/**\n * Update user profile\n */ const updateUserProfile = async (userId, profileData)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", userId), {\n            ...profileData,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        });\n    } catch (error) {\n        console.error(\"Error updating user profile:\", error);\n        throw error;\n    }\n};\n/**\n * Change user role (admin only)\n */ const changeUserRole = async (userId, newRole)=>{\n    try {\n        // Update role in user document\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", userId), {\n            role: newRole,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        });\n        // Update role in username mapping if it exists\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", userId));\n        if (userDoc.exists()) {\n            const { username, email } = userDoc.data();\n            if (username) {\n                // Check if username mapping exists\n                const usernameDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()));\n                if (usernameDoc.exists()) {\n                    // Update existing mapping\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()), {\n                        role: newRole\n                    });\n                } else {\n                    // Create missing username mapping\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()), {\n                        uid: userId,\n                        email: email,\n                        role: newRole\n                    });\n                }\n            }\n        }\n        console.log(`User role changed to ${newRole} for user ${userId}`);\n    } catch (error) {\n        console.error(\"Error changing user role:\", error);\n        throw error;\n    }\n};\n/**\n * Get all users (admin function)\n */ const getAllUsers = async ()=>{\n    try {\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\"));\n        const users = usersSnapshot.docs.map((doc)=>({\n                uid: doc.id,\n                ...doc.data()\n            }));\n        console.log(`Found ${users.length} total users`);\n        return users;\n    } catch (error) {\n        console.error(\"Error getting all users:\", error);\n        throw error;\n    }\n};\n/**\n * Get users by role\n */ const getUsersByRole = async (role)=>{\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)(\"role\", \"==\", role));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const users = querySnapshot.docs.map((doc)=>({\n                uid: doc.id,\n                ...doc.data()\n            }));\n        return users;\n    } catch (error) {\n        console.error(\"Error getting users by role:\", error);\n        throw error;\n    }\n};\n/**\n * Send password reset email (using email, not username)\n */ const resetPassword = async (email)=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.sendPasswordResetEmail)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email);\n    } catch (error) {\n        console.error(\"Error sending password reset email:\", error);\n        throw error;\n    }\n};\n/**\n * Check if username is available\n */ const checkUsernameAvailability = async (username)=>{\n    try {\n        const usernameDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()));\n        return !usernameDoc.exists();\n    } catch (error) {\n        console.error(\"Error checking username availability:\", error);\n        throw error;\n    }\n};\n/**\n * Create admin user (for initial setup)\n */ const createAdminUser = async ()=>{\n    try {\n        const adminUsername = process.env.ADMIN_USERNAME || \"admin\";\n        const adminPassword = process.env.ADMIN_PASSWORD || \"GHC2024@Admin\";\n        const adminEmail = \"<EMAIL>\";\n        // Check if admin already exists\n        const adminExists = await checkUsernameAvailability(adminUsername);\n        if (!adminExists) {\n            console.log(\"Admin user already exists\");\n            return;\n        }\n        const adminData = await registerUser(adminUsername, adminEmail, adminPassword, USER_ROLES.ADMIN, {\n            name: \"System Administrator\",\n            employeeId: \"ADM001\"\n        });\n        console.log(\"Admin user created successfully:\", adminData);\n        return adminData;\n    } catch (error) {\n        console.error(\"Error creating admin user:\", error);\n        throw error;\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/authService.js\n");

/***/ }),

/***/ "./lib/firebase.js":
/*!*************************!*\
  !*** ./lib/firebase.js ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"firebase/app\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__]);\n([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDVN4je66aWey2aHTg3PWQXAbJSDIUIGg4\",\n    authDomain: \"ghc-sec-school.firebaseapp.com\",\n    projectId: \"ghc-sec-school\",\n    storageBucket: \"ghc-sec-school.firebasestorage.app\",\n    messagingSenderId: \"916387441873\",\n    appId: \"1:916387441873:web:3a74322c4e693ff993e9f0\"\n};\n// Initialize Firebase (prevent multiple initialization)\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length === 0 ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n// Log successful initialization\nconsole.log(\"Firebase initialized successfully:\", {\n    appName: app.name,\n    projectId: app.options.projectId\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/firebase.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\_app.js\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\_app.js\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDd0I7QUFFdkMsU0FBU0MsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFDRSw4REFBQ0gsK0RBQVlBO2tCQUNYLDRFQUFDRTtZQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7O0FBRzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3JlYXQtaGVyaXRhZ2UtY29sbGVnZS1mcm9udGVuZC8uL3BhZ2VzL19hcHAuanM/ZTBhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcydcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJy4uL2NvbnRleHRzL0F1dGhDb250ZXh0J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9KSB7XG4gIHJldHVybiAoXG4gICAgPEF1dGhQcm92aWRlcj5cbiAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICA8L0F1dGhQcm92aWRlcj5cbiAgKVxufSJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/dashboard/admin.js":
/*!**********************************!*\
  !*** ./pages/dashboard/admin.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/DashboardLayout */ \"./components/DashboardLayout.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ProtectedRoute */ \"./components/ProtectedRoute.js\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/authService */ \"./lib/authService.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__, _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__, _lib_authService__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__, _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__, _lib_authService__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n// Professional SVG Icons\nconst UsersIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 9,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined);\nconst AcademicCapIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l9-5-9-5-9 5 9 5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                lineNumber: 15,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                lineNumber: 16,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined);\nconst BuildingIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 22,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined);\nconst CurrencyIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 28,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\nconst ChartBarIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined);\nconst CogIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                lineNumber: 40,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                lineNumber: 41,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\nfunction AdminDashboard() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalStudents: 0,\n        totalTeachers: 0,\n        totalClasses: 0,\n        pendingPayments: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [recentActivities, setRecentActivities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            const users = await (0,_lib_authService__WEBPACK_IMPORTED_MODULE_4__.getAllUsers)();\n            const userStats = {\n                totalStudents: users.filter((u)=>u.role === \"student\").length,\n                totalTeachers: users.filter((u)=>u.role === \"teacher\").length,\n                totalClasses: 0,\n                pendingPayments: 0\n            };\n            setStats(userStats);\n            const activities = users.slice(0, 5).map((user, index)=>({\n                    id: index + 1,\n                    type: \"user\",\n                    message: `${user.role} ${user.name} is active in system`,\n                    time: user.createdAt?.toDate?.()?.toLocaleDateString() || \"Recently\"\n                }));\n            setRecentActivities(activities);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const quickActions = [\n        {\n            name: \"User Management\",\n            href: \"/dashboard/admin/users\",\n            icon: UsersIcon,\n            color: \"bg-blue-500\",\n            description: \"Manage users and roles\"\n        },\n        {\n            name: \"Academic Setup\",\n            href: \"/dashboard/admin/academic\",\n            icon: AcademicCapIcon,\n            color: \"bg-green-500\",\n            description: \"Classes, subjects & sessions\"\n        },\n        {\n            name: \"Reports & Analytics\",\n            href: \"/dashboard/admin/reports\",\n            icon: ChartBarIcon,\n            color: \"bg-purple-500\",\n            description: \"View system reports\"\n        },\n        {\n            name: \"System Settings\",\n            href: \"/dashboard/admin/settings\",\n            icon: CogIcon,\n            color: \"bg-gray-600\",\n            description: \"Configure system\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        allowedRoles: [\n            \"admin\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold mb-2\",\n                                            children: \"Administrator Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-100\",\n                                            children: \"Manage your school system efficiently\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-sm rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CogIcon, {\n                                            className: \"w-12 h-12 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: loading ? Array.from({\n                            length: 4\n                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6 animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-gray-200 rounded mb-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                        children: \"Total Students\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                        children: stats.totalStudents\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: \"Live count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-blue-50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UsersIcon, {\n                                                    className: \"w-8 h-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 152,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                        children: \"Total Teachers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                        children: stats.totalTeachers\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: \"Live count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-green-50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                    className: \"w-8 h-8 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                        children: \"Active Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                        children: stats.totalClasses\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: \"Across all Classes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-purple-50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingIcon, {\n                                                    className: \"w-8 h-8 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 178,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                        children: \"Pending Payments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                        children: stats.pendingPayments\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-orange-600 mt-1\",\n                                                        children: \"Requires attention\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-orange-50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrencyIcon, {\n                                                    className: \"w-8 h-8 text-orange-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 191,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Quick Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Manage your school system\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: quickActions.map((action)=>{\n                                    const IconComponent = action.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: action.href,\n                                        className: \"group relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 hover:from-white hover:to-gray-50 border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `p-3 rounded-lg ${action.color} group-hover:scale-110 transition-transform duration-300`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 group-hover:text-primary transition-colors\",\n                                                                children: action.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: action.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-gray-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, action.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Recent Activities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard/admin/activities\",\n                                        className: \"text-sm text-primary hover:text-primary/80 font-medium\",\n                                        children: \"View All\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: recentActivities.map((activity)=>{\n                                    const getActivityIcon = (type)=>{\n                                        switch(type){\n                                            case \"user\":\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UsersIcon, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 28\n                                                }, this);\n                                            case \"payment\":\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrencyIcon, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 28\n                                                }, this);\n                                            case \"system\":\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CogIcon, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 28\n                                                }, this);\n                                            case \"academic\":\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 28\n                                                }, this);\n                                            default:\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartBarIcon, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 28\n                                                }, this);\n                                        }\n                                    };\n                                    const getActivityColor = (type)=>{\n                                        switch(type){\n                                            case \"user\":\n                                                return \"bg-blue-50 text-blue-600 border-blue-100\";\n                                            case \"payment\":\n                                                return \"bg-green-50 text-green-600 border-green-100\";\n                                            case \"system\":\n                                                return \"bg-gray-50 text-gray-600 border-gray-100\";\n                                            case \"academic\":\n                                                return \"bg-purple-50 text-purple-600 border-purple-100\";\n                                            default:\n                                                return \"bg-orange-50 text-orange-600 border-orange-100\";\n                                        }\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-4 rounded-lg border border-gray-100 hover:bg-gray-50 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `p-2 rounded-lg border ${getActivityColor(activity.type)}`,\n                                                children: getActivityIcon(activity.type)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4 flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: activity.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: activity.time\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 293,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, activity.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard/admin.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "firebase/app":
/*!*******************************!*\
  !*** external "firebase/app" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/app");;

/***/ }),

/***/ "firebase/auth":
/*!********************************!*\
  !*** external "firebase/auth" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/auth");;

/***/ }),

/***/ "firebase/firestore":
/*!*************************************!*\
  !*** external "firebase/firestore" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/firestore");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();