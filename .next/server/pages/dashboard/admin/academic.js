/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/dashboard/admin/academic";
exports.ids = ["pages/dashboard/admin/academic"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin%2Facademic&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin%5Cacademic.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin%2Facademic&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin%5Cacademic.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\dashboard\\admin\\academic.js */ \"./pages/dashboard/admin/academic.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/dashboard/admin/academic\",\n        pathname: \"/dashboard/admin/academic\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_dashboard_admin_academic_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin%2Facademic&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin%5Cacademic.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/DashboardLayout.js":
/*!***************************************!*\
  !*** ./components/DashboardLayout.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { userProfile, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const user = userProfile || {\n        name: \"Loading...\",\n        role: \"admin\"\n    };\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n            router.push(\"/login\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            router.push(\"/login\");\n        }\n    };\n    // Navigation items based on user role\n    // Professional SVG Icons for Navigation\n    const DashboardIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 28,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 29,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 27,\n            columnNumber: 5\n        }, this);\n    const UsersIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, this);\n    const CogIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 41,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 42,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, this);\n    const AcademicIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 14l9-5-9-5-9 5 9 5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 48,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 49,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 47,\n            columnNumber: 5\n        }, this);\n    const ChartIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 54,\n            columnNumber: 5\n        }, this);\n    const CalendarIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 60,\n            columnNumber: 5\n        }, this);\n    const DocumentIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 66,\n            columnNumber: 5\n        }, this);\n    const navigationItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard/admin\",\n            icon: DashboardIcon\n        },\n        {\n            name: \"User Management\",\n            href: \"/dashboard/admin/users\",\n            icon: UsersIcon\n        },\n        {\n            name: \"Academic Setup\",\n            href: \"/dashboard/admin/academic\",\n            icon: AcademicIcon\n        },\n        {\n            name: \"Reports\",\n            href: \"/dashboard/admin/reports\",\n            icon: ChartIcon\n        },\n        {\n            name: \"Settings\",\n            href: \"/dashboard/admin/settings\",\n            icon: CogIcon\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-16 px-4 border-b border-gray-200 flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: \"/GHC.jpg\",\n                                    alt: \"Great Heritage College\",\n                                    width: 36,\n                                    height: 36,\n                                    className: \"rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary font-bold text-lg block\",\n                                            children: \"GHC Portal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 capitalize\",\n                                            children: [\n                                                user?.role,\n                                                \" Panel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 px-4 py-6 overflow-y-auto\",\n                        children: navigationItems.map((item)=>{\n                            const IconComponent = item.icon;\n                            const isActive = router.pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: item.href,\n                                className: `flex items-center px-4 py-3 mb-2 rounded-lg transition-all duration-200 ${isActive ? \"bg-primary text-white shadow-md\" : \"text-gray-700 hover:bg-gray-100 hover:text-primary\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-3 font-medium\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, item.name, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white border-b border-gray-200 shadow-sm flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                            className: \"lg:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        \"Good \",\n                                                        new Date().getHours() < 12 ? \"Morning\" : new Date().getHours() < 18 ? \"Afternoon\" : \"Evening\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        user.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12M1.01 11.88l3.12 3.12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 px-3 py-2 rounded-lg bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-primary rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-semibold text-sm\",\n                                                        children: user.name.charAt(0).toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 capitalize\",\n                                                            children: user.role\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"p-2 rounded-lg text-gray-600 hover:text-red-600 hover:bg-red-50 transition-colors\",\n                                            title: \"Logout\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-6 bg-gray-50 overflow-y-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/DashboardLayout.js\n");

/***/ }),

/***/ "./components/ProtectedRoute.js":
/*!**************************************!*\
  !*** ./components/ProtectedRoute.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction ProtectedRoute({ children, allowedRoles = [] }) {\n    const { currentUser, userProfile, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (!loading) {\n            if (!currentUser) {\n                setIsRedirecting(true);\n                router.push(`/login?from=${encodeURIComponent(router.asPath)}`);\n                return;\n            }\n            if (!userProfile) {\n                setIsRedirecting(true);\n                router.push(`/login?from=${encodeURIComponent(router.asPath)}`);\n                return;\n            }\n            // Role-based check\n            if (allowedRoles.length > 0 && !allowedRoles.includes(userProfile.role)) {\n                setIsRedirecting(true);\n                router.push(\"/403\");\n                return;\n            }\n            // If we get here, user is authorized\n            setIsRedirecting(false);\n        }\n    }, [\n        currentUser,\n        userProfile,\n        loading,\n        router,\n        allowedRoles\n    ]);\n    // Show loading spinner\n    if (loading || isRedirecting) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-primary/5 via-white to-accent/5 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        src: \"/GHC.jpg\",\n                        alt: \"Great Heritage College\",\n                        width: 80,\n                        height: 80,\n                        className: \"rounded-full mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\ProtectedRoute.js\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\ProtectedRoute.js\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: loading ? \"Loading...\" : \"Redirecting...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\ProtectedRoute.js\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\ProtectedRoute.js\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\ProtectedRoute.js\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if user is not authenticated\n    if (!currentUser || !userProfile) {\n        return null;\n    }\n    // Don't render if user doesn't have required role\n    if (allowedRoles.length > 0 && !allowedRoles.includes(userProfile.role)) {\n        return null;\n    }\n    // Render children if all checks pass\n    return children;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ProtectedRoute.js\n");

/***/ }),

/***/ "./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/firebase */ \"./lib/firebase.js\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authService */ \"./lib/authService.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase__WEBPACK_IMPORTED_MODULE_3__, _lib_authService__WEBPACK_IMPORTED_MODULE_4__]);\n([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase__WEBPACK_IMPORTED_MODULE_3__, _lib_authService__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    currentUser: null,\n    userProfile: null,\n    loading: true,\n    isAdmin: false,\n    isProprietor: false,\n    isTeacher: false,\n    isStudent: false,\n    isParent: false,\n    hasRole: ()=>false,\n    login: async ()=>{},\n    logout: async ()=>{},\n    refreshUserProfile: async ()=>{}\n});\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\nfunction AuthProvider({ children }) {\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, async (user)=>{\n            setCurrentUser(user);\n            if (user) {\n                try {\n                    const profileData = await (0,_lib_authService__WEBPACK_IMPORTED_MODULE_4__.getUserProfile)(user.uid);\n                    if (profileData) {\n                        console.log(\"User profile loaded:\", {\n                            uid: user.uid,\n                            username: profileData.username,\n                            role: profileData.role,\n                            name: profileData.name\n                        });\n                        setUserProfile(profileData);\n                    } else {\n                        console.warn(\"No profile found for user:\", user.uid);\n                        setUserProfile(null);\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching user profile:\", error);\n                    setUserProfile(null);\n                }\n            } else {\n                setUserProfile(null);\n            }\n            setLoading(false);\n        });\n        return unsubscribe;\n    }, []);\n    // Role helpers\n    const isAdmin = userProfile?.role === \"admin\";\n    const isProprietor = userProfile?.role === \"proprietor\";\n    const isTeacher = userProfile?.role === \"teacher\";\n    const isStudent = userProfile?.role === \"student\";\n    const isParent = userProfile?.role === \"parent\";\n    const hasRole = (roles)=>{\n        if (!userProfile) return false;\n        return Array.isArray(roles) ? roles.includes(userProfile.role) : userProfile.role === roles;\n    };\n    const login = async (username, password)=>{\n        return (0,_lib_authService__WEBPACK_IMPORTED_MODULE_4__.loginWithUsername)(username, password);\n    };\n    const logout = async ()=>{\n        return (0,_lib_authService__WEBPACK_IMPORTED_MODULE_4__.logoutUser)();\n    };\n    const refreshUserProfile = async ()=>{\n        if (!currentUser) return;\n        try {\n            const profileData = await (0,_lib_authService__WEBPACK_IMPORTED_MODULE_4__.getUserProfile)(currentUser.uid);\n            setUserProfile(profileData);\n            console.log(\"User profile refreshed:\", profileData?.role);\n        } catch (error) {\n            console.error(\"Error refreshing user profile:\", error);\n        }\n    };\n    const value = {\n        currentUser,\n        userProfile,\n        loading,\n        isAdmin,\n        isProprietor,\n        isTeacher,\n        isStudent,\n        isParent,\n        hasRole,\n        login,\n        logout,\n        refreshUserProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/AuthContext.js\n");

/***/ }),

/***/ "./lib/academicService.js":
/*!********************************!*\
  !*** ./lib/academicService.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClass: () => (/* binding */ createClass),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   createSubject: () => (/* binding */ createSubject),\n/* harmony export */   deleteClass: () => (/* binding */ deleteClass),\n/* harmony export */   deleteSession: () => (/* binding */ deleteSession),\n/* harmony export */   deleteSubject: () => (/* binding */ deleteSubject),\n/* harmony export */   getAllClasses: () => (/* binding */ getAllClasses),\n/* harmony export */   getAllSessions: () => (/* binding */ getAllSessions),\n/* harmony export */   getAllSubjects: () => (/* binding */ getAllSubjects),\n/* harmony export */   searchTeachers: () => (/* binding */ searchTeachers),\n/* harmony export */   updateClass: () => (/* binding */ updateClass),\n/* harmony export */   updateSession: () => (/* binding */ updateSession),\n/* harmony export */   updateSubject: () => (/* binding */ updateSubject)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"./lib/firebase.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_firestore__WEBPACK_IMPORTED_MODULE_0__, _firebase__WEBPACK_IMPORTED_MODULE_1__]);\n([firebase_firestore__WEBPACK_IMPORTED_MODULE_0__, _firebase__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n/**\n * Classes Management\n */ const createClass = async (classData)=>{\n    try {\n        const classRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"classes\"));\n        const newClass = {\n            id: classRef.id,\n            ...classData,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)(),\n            isActive: true\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)(classRef, newClass);\n        return newClass;\n    } catch (error) {\n        console.error(\"Error creating class:\", error);\n        throw error;\n    }\n};\nconst getAllClasses = async ()=>{\n    try {\n        const classesSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"classes\"));\n        return classesSnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data()\n            }));\n    } catch (error) {\n        console.error(\"Error getting classes:\", error);\n        throw error;\n    }\n};\nconst updateClass = async (classId, classData)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"classes\", classId), {\n            ...classData,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        });\n    } catch (error) {\n        console.error(\"Error updating class:\", error);\n        throw error;\n    }\n};\nconst deleteClass = async (classId)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"classes\", classId));\n    } catch (error) {\n        console.error(\"Error deleting class:\", error);\n        throw error;\n    }\n};\n/**\n * Subjects Management\n */ const createSubject = async (subjectData)=>{\n    try {\n        const subjectRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"subjects\"));\n        const newSubject = {\n            id: subjectRef.id,\n            ...subjectData,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)(),\n            isActive: true\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)(subjectRef, newSubject);\n        return newSubject;\n    } catch (error) {\n        console.error(\"Error creating subject:\", error);\n        throw error;\n    }\n};\nconst getAllSubjects = async ()=>{\n    try {\n        const subjectsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"subjects\"));\n        return subjectsSnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data()\n            }));\n    } catch (error) {\n        console.error(\"Error getting subjects:\", error);\n        throw error;\n    }\n};\nconst updateSubject = async (subjectId, subjectData)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"subjects\", subjectId), {\n            ...subjectData,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        });\n    } catch (error) {\n        console.error(\"Error updating subject:\", error);\n        throw error;\n    }\n};\nconst deleteSubject = async (subjectId)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"subjects\", subjectId));\n    } catch (error) {\n        console.error(\"Error deleting subject:\", error);\n        throw error;\n    }\n};\n/**\n * Academic Sessions Management\n */ const createSession = async (sessionData)=>{\n    try {\n        const sessionRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"sessions\"));\n        const newSession = {\n            id: sessionRef.id,\n            ...sessionData,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)(sessionRef, newSession);\n        return newSession;\n    } catch (error) {\n        console.error(\"Error creating session:\", error);\n        throw error;\n    }\n};\nconst getAllSessions = async ()=>{\n    try {\n        const sessionsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"sessions\"));\n        return sessionsSnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data()\n            }));\n    } catch (error) {\n        console.error(\"Error getting sessions:\", error);\n        throw error;\n    }\n};\nconst updateSession = async (sessionId, sessionData)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"sessions\", sessionId), {\n            ...sessionData,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.serverTimestamp)()\n        });\n    } catch (error) {\n        console.error(\"Error updating session:\", error);\n        throw error;\n    }\n};\nconst deleteSession = async (sessionId)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"sessions\", sessionId));\n    } catch (error) {\n        console.error(\"Error deleting session:\", error);\n        throw error;\n    }\n};\n/**\n * Teacher Search for Class Assignment\n */ const searchTeachers = async (searchTerm = \"\")=>{\n    try {\n        const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"users\");\n        const teachersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"role\", \"==\", \"teacher\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.where)(\"isActive\", \"==\", true));\n        const teachersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(teachersQuery);\n        let teachers = teachersSnapshot.docs.map((doc)=>({\n                uid: doc.id,\n                ...doc.data()\n            }));\n        // Filter by search term if provided\n        if (searchTerm) {\n            const term = searchTerm.toLowerCase();\n            teachers = teachers.filter((teacher)=>teacher.name?.toLowerCase().includes(term) || teacher.username?.toLowerCase().includes(term));\n        }\n        return teachers;\n    } catch (error) {\n        console.error(\"Error searching teachers:\", error);\n        throw error;\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/academicService.js\n");

/***/ }),

/***/ "./lib/authService.js":
/*!****************************!*\
  !*** ./lib/authService.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES),\n/* harmony export */   changeUserRole: () => (/* binding */ changeUserRole),\n/* harmony export */   checkUsernameAvailability: () => (/* binding */ checkUsernameAvailability),\n/* harmony export */   createAdminUser: () => (/* binding */ createAdminUser),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUsersByRole: () => (/* binding */ getUsersByRole),\n/* harmony export */   loginWithUsername: () => (/* binding */ loginWithUsername),\n/* harmony export */   logoutUser: () => (/* binding */ logoutUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   resetPassword: () => (/* binding */ resetPassword),\n/* harmony export */   updateUserProfile: () => (/* binding */ updateUserProfile)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firebase */ \"./lib/firebase.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_auth__WEBPACK_IMPORTED_MODULE_0__, firebase_firestore__WEBPACK_IMPORTED_MODULE_1__, _firebase__WEBPACK_IMPORTED_MODULE_2__]);\n([firebase_auth__WEBPACK_IMPORTED_MODULE_0__, firebase_firestore__WEBPACK_IMPORTED_MODULE_1__, _firebase__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n/**\n * School roles\n */ const USER_ROLES = {\n    ADMIN: \"admin\",\n    PROPRIETOR: \"proprietor\",\n    TEACHER: \"teacher\",\n    STUDENT: \"student\",\n    PARENT: \"parent\"\n};\n/**\n * Login with username instead of email\n */ const loginWithUsername = async (username, password)=>{\n    try {\n        // First, get the email associated with this username\n        const usernameDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()));\n        if (!usernameDoc.exists()) {\n            throw new Error(\"Username not found\");\n        }\n        const { email } = usernameDoc.data();\n        // Now login with email and password\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email, password);\n        return userCredential.user;\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        throw error;\n    }\n};\n/**\n * Register a new user with username\n */ const registerUser = async (username, email, password, role = USER_ROLES.STUDENT, profileData = {})=>{\n    try {\n        // Check if username already exists\n        const usernameDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()));\n        if (usernameDoc.exists()) {\n            throw new Error(\"Username already exists\");\n        }\n        // Auto-generate email for students if not provided\n        let finalEmail = email;\n        if (!email && role === USER_ROLES.STUDENT) {\n            finalEmail = `${username.toLowerCase()}@student.greatheritagecollege.edu`;\n        }\n        if (!finalEmail) {\n            throw new Error(\"Email is required for non-student users\");\n        }\n        // Create user in Firebase Auth\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, finalEmail, password);\n        const user = userCredential.user;\n        // Set display name\n        const displayName = profileData.name || username;\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.updateProfile)(user, {\n            displayName\n        });\n        // Create user document in Firestore\n        const userData = {\n            uid: user.uid,\n            username: username.toLowerCase(),\n            email: user.email,\n            name: displayName,\n            role,\n            isActive: true,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)(),\n            ...profileData\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", user.uid), userData);\n        // Create username mapping\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()), {\n            uid: user.uid,\n            email: user.email,\n            role\n        });\n        return userData;\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        throw error;\n    }\n};\n/**\n * Logout user\n */ const logoutUser = async ()=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n    } catch (error) {\n        console.error(\"Logout error:\", error);\n        throw error;\n    }\n};\n/**\n * Get user profile from Firestore\n */ const getUserProfile = async (userId)=>{\n    try {\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", userId));\n        if (userDoc.exists()) {\n            return userDoc.data();\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error getting user profile:\", error);\n        throw error;\n    }\n};\n/**\n * Update user profile\n */ const updateUserProfile = async (userId, profileData)=>{\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", userId), {\n            ...profileData,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        });\n    } catch (error) {\n        console.error(\"Error updating user profile:\", error);\n        throw error;\n    }\n};\n/**\n * Change user role (admin only)\n */ const changeUserRole = async (userId, newRole)=>{\n    try {\n        // Update role in user document\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", userId), {\n            role: newRole,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        });\n        // Update role in username mapping if it exists\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", userId));\n        if (userDoc.exists()) {\n            const { username, email } = userDoc.data();\n            if (username) {\n                // Check if username mapping exists\n                const usernameDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()));\n                if (usernameDoc.exists()) {\n                    // Update existing mapping\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()), {\n                        role: newRole\n                    });\n                } else {\n                    // Create missing username mapping\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()), {\n                        uid: userId,\n                        email: email,\n                        role: newRole\n                    });\n                }\n            }\n        }\n        console.log(`User role changed to ${newRole} for user ${userId}`);\n    } catch (error) {\n        console.error(\"Error changing user role:\", error);\n        throw error;\n    }\n};\n/**\n * Get all users (admin function)\n */ const getAllUsers = async ()=>{\n    try {\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\"));\n        const users = usersSnapshot.docs.map((doc)=>({\n                uid: doc.id,\n                ...doc.data()\n            }));\n        console.log(`Found ${users.length} total users`);\n        return users;\n    } catch (error) {\n        console.error(\"Error getting all users:\", error);\n        throw error;\n    }\n};\n/**\n * Get users by role\n */ const getUsersByRole = async (role)=>{\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"users\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)(\"role\", \"==\", role));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        const users = querySnapshot.docs.map((doc)=>({\n                uid: doc.id,\n                ...doc.data()\n            }));\n        return users;\n    } catch (error) {\n        console.error(\"Error getting users by role:\", error);\n        throw error;\n    }\n};\n/**\n * Send password reset email (using email, not username)\n */ const resetPassword = async (email)=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.sendPasswordResetEmail)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email);\n    } catch (error) {\n        console.error(\"Error sending password reset email:\", error);\n        throw error;\n    }\n};\n/**\n * Check if username is available\n */ const checkUsernameAvailability = async (username)=>{\n    try {\n        const usernameDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usernames\", username.toLowerCase()));\n        return !usernameDoc.exists();\n    } catch (error) {\n        console.error(\"Error checking username availability:\", error);\n        throw error;\n    }\n};\n/**\n * Create admin user (for initial setup)\n */ const createAdminUser = async ()=>{\n    try {\n        const adminUsername = process.env.ADMIN_USERNAME || \"admin\";\n        const adminPassword = process.env.ADMIN_PASSWORD || \"GHC2024@Admin\";\n        const adminEmail = \"<EMAIL>\";\n        // Check if admin already exists\n        const adminExists = await checkUsernameAvailability(adminUsername);\n        if (!adminExists) {\n            console.log(\"Admin user already exists\");\n            return;\n        }\n        const adminData = await registerUser(adminUsername, adminEmail, adminPassword, USER_ROLES.ADMIN, {\n            name: \"System Administrator\",\n            employeeId: \"ADM001\"\n        });\n        console.log(\"Admin user created successfully:\", adminData);\n        return adminData;\n    } catch (error) {\n        console.error(\"Error creating admin user:\", error);\n        throw error;\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/authService.js\n");

/***/ }),

/***/ "./lib/firebase.js":
/*!*************************!*\
  !*** ./lib/firebase.js ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"firebase/app\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__]);\n([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDVN4je66aWey2aHTg3PWQXAbJSDIUIGg4\",\n    authDomain: \"ghc-sec-school.firebaseapp.com\",\n    projectId: \"ghc-sec-school\",\n    storageBucket: \"ghc-sec-school.firebasestorage.app\",\n    messagingSenderId: \"916387441873\",\n    appId: \"1:916387441873:web:3a74322c4e693ff993e9f0\"\n};\n// Initialize Firebase (prevent multiple initialization)\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length === 0 ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n// Log successful initialization\nconsole.log(\"Firebase initialized successfully:\", {\n    appName: app.name,\n    projectId: app.options.projectId\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/firebase.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\_app.js\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\_app.js\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDd0I7QUFFdkMsU0FBU0MsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFDRSw4REFBQ0gsK0RBQVlBO2tCQUNYLDRFQUFDRTtZQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7O0FBRzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3JlYXQtaGVyaXRhZ2UtY29sbGVnZS1mcm9udGVuZC8uL3BhZ2VzL19hcHAuanM/ZTBhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcydcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJy4uL2NvbnRleHRzL0F1dGhDb250ZXh0J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9KSB7XG4gIHJldHVybiAoXG4gICAgPEF1dGhQcm92aWRlcj5cbiAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICA8L0F1dGhQcm92aWRlcj5cbiAgKVxufSJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/dashboard/admin/academic.js":
/*!*******************************************!*\
  !*** ./pages/dashboard/admin/academic.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminAcademic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/DashboardLayout */ \"./components/DashboardLayout.js\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/ProtectedRoute */ \"./components/ProtectedRoute.js\");\n/* harmony import */ var _lib_academicService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../lib/academicService */ \"./lib/academicService.js\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../lib/authService */ \"./lib/authService.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__, _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__, _lib_academicService__WEBPACK_IMPORTED_MODULE_5__, _lib_authService__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__, _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__, _lib_academicService__WEBPACK_IMPORTED_MODULE_5__, _lib_authService__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n// Professional SVG Icons\nconst AcademicCapIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l9-5-9-5-9 5 9 5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 22,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 20,\n        columnNumber: 3\n    }, undefined);\nconst PlusIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 28,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\nconst EditIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined);\nconst TrashIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\nconst SaveIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3-3-3m3-3v12\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 46,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined);\nfunction AdminAcademic() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"classes\");\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gradingSystem, setGradingSystem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"percentage\",\n        passMarkPercentage: 40,\n        grades: [\n            {\n                grade: \"A\",\n                minScore: 80,\n                maxScore: 100,\n                points: 5,\n                remark: \"Excellent\"\n            },\n            {\n                grade: \"B\",\n                minScore: 70,\n                maxScore: 79,\n                points: 4,\n                remark: \"Very Good\"\n            },\n            {\n                grade: \"C\",\n                minScore: 60,\n                maxScore: 69,\n                points: 3,\n                remark: \"Good\"\n            },\n            {\n                grade: \"D\",\n                minScore: 50,\n                maxScore: 59,\n                points: 2,\n                remark: \"Pass\"\n            },\n            {\n                grade: \"E\",\n                minScore: 40,\n                maxScore: 49,\n                points: 1,\n                remark: \"Poor\"\n            },\n            {\n                grade: \"F\",\n                minScore: 0,\n                maxScore: 39,\n                points: 0,\n                remark: \"Fail\"\n            }\n        ]\n    });\n    const [showGradeModal, setShowGradeModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newGrade, setNewGrade] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editingGradeIndex, setEditingGradeIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalType, setModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [teacherSearch, setTeacherSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedTeacher, setSelectedTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newItem, setNewItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [filteredTeachers, setFilteredTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTeacherDropdown, setShowTeacherDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchingTeachers, setSearchingTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAcademicData();\n    }, []);\n    const loadAcademicData = async ()=>{\n        try {\n            console.log(\"Loading academic data...\");\n            const [classesData, subjectsData, sessionsData, teachersData] = await Promise.all([\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllClasses)(),\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllSubjects)(),\n                (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.getAllSessions)(),\n                getAllUsers()\n            ]);\n            // Filter teachers from all users\n            const teachersOnly = teachersData.filter((user)=>user.role === \"teacher\");\n            console.log(\"All users:\", teachersData.length, \"Teachers found:\", teachersOnly.length, teachersOnly);\n            setClasses(classesData);\n            setSubjects(subjectsData);\n            setSessions(sessionsData);\n            setTeachers(teachersOnly);\n            setFilteredTeachers(teachersOnly);\n        // Keep existing grading system\n        } catch (error) {\n            console.error(\"Error loading academic data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            // Mock API call - replace with actual implementation\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            alert(\"Academic setup saved successfully!\");\n        } catch (error) {\n            console.error(\"Error saving academic setup:\", error);\n            alert(\"Error saving academic setup. Please try again.\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleDelete = async (type, id)=>{\n        if (confirm(`Are you sure you want to delete this ${type}?`)) {\n            try {\n                switch(type){\n                    case \"class\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteClass)(id);\n                        setClasses(classes.filter((c)=>c.id !== id));\n                        break;\n                    case \"subject\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteSubject)(id);\n                        setSubjects(subjects.filter((s)=>s.id !== id));\n                        break;\n                    case \"session\":\n                        await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.deleteSession)(id);\n                        setSessions(sessions.filter((s)=>s.id !== id));\n                        break;\n                }\n                alert(`${type} deleted successfully!`);\n            } catch (error) {\n                console.error(`Error deleting ${type}:`, error);\n                alert(`Error deleting ${type}. Please try again.`);\n            }\n        }\n    };\n    const openAddModal = (type)=>{\n        setModalType(type);\n        setNewItem({});\n        setSelectedTeacher(null);\n        setTeacherSearch(\"\");\n        setFilteredTeachers([]);\n        setShowTeacherDropdown(false);\n        setSearchingTeachers(false);\n        setShowAddModal(true);\n    };\n    const handleAddItem = async ()=>{\n        try {\n            setIsSaving(true);\n            let result;\n            switch(modalType){\n                case \"class\":\n                    const classData = {\n                        ...newItem,\n                        classTeacherId: selectedTeacher?.uid,\n                        classTeacherName: selectedTeacher?.name,\n                        classTeacherUsername: selectedTeacher?.username,\n                        currentStudents: 0,\n                        capacity: 0 // Will be calculated based on enrolled students\n                    };\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createClass)(classData);\n                    setClasses([\n                        ...classes,\n                        result\n                    ]);\n                    break;\n                case \"subject\":\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createSubject)(newItem);\n                    setSubjects([\n                        ...subjects,\n                        result\n                    ]);\n                    break;\n                case \"session\":\n                    result = await (0,_lib_academicService__WEBPACK_IMPORTED_MODULE_5__.createSession)(newItem);\n                    setSessions([\n                        ...sessions,\n                        result\n                    ]);\n                    break;\n            }\n            setShowAddModal(false);\n            alert(`${modalType} created successfully!`);\n        } catch (error) {\n            console.error(`Error creating ${modalType}:`, error);\n            alert(`Error creating ${modalType}. Please try again.`);\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleTeacherSearch = async (searchTerm)=>{\n        setTeacherSearch(searchTerm);\n        setShowTeacherDropdown(true);\n        if (searchTerm.trim().length > 0) {\n            setSearchingTeachers(true);\n            // Small delay to show spinner\n            setTimeout(()=>{\n                const filtered = teachers.filter((teacher)=>teacher.name?.toLowerCase().includes(searchTerm.toLowerCase()) || teacher.username?.toLowerCase().includes(searchTerm.toLowerCase()));\n                setFilteredTeachers(filtered);\n                setSearchingTeachers(false);\n            }, 200);\n        } else {\n            setFilteredTeachers(teachers);\n            setSearchingTeachers(false);\n        }\n    };\n    const addGrade = ()=>{\n        if (newGrade.grade && newGrade.minScore !== undefined && newGrade.maxScore !== undefined) {\n            const updatedGrades = [\n                ...gradingSystem.grades,\n                {\n                    ...newGrade,\n                    points: gradingSystem.grades.length\n                }\n            ];\n            setGradingSystem({\n                ...gradingSystem,\n                grades: updatedGrades\n            });\n            setNewGrade({});\n            setShowGradeModal(false);\n        }\n    };\n    const editGrade = (index)=>{\n        setNewGrade(gradingSystem.grades[index]);\n        setEditingGradeIndex(index);\n        setShowGradeModal(true);\n    };\n    const updateGrade = ()=>{\n        const updatedGrades = [\n            ...gradingSystem.grades\n        ];\n        updatedGrades[editingGradeIndex] = newGrade;\n        setGradingSystem({\n            ...gradingSystem,\n            grades: updatedGrades\n        });\n        setNewGrade({});\n        setEditingGradeIndex(-1);\n        setShowGradeModal(false);\n    };\n    const deleteGrade = (index)=>{\n        if (confirm(\"Are you sure you want to delete this grade?\")) {\n            const updatedGrades = gradingSystem.grades.filter((_, i)=>i !== index);\n            setGradingSystem({\n                ...gradingSystem,\n                grades: updatedGrades\n            });\n        }\n    };\n    const tabs = [\n        {\n            id: \"classes\",\n            name: \"Classes\",\n            icon: \"\\uD83C\\uDFEB\"\n        },\n        {\n            id: \"subjects\",\n            name: \"Subjects\",\n            icon: \"\\uD83D\\uDCDA\"\n        },\n        {\n            id: \"sessions\",\n            name: \"Sessions\",\n            icon: \"\\uD83D\\uDCC5\"\n        },\n        {\n            id: \"grading\",\n            name: \"Grading System\",\n            icon: \"\\uD83D\\uDCCA\"\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 268,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 267,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n            lineNumber: 266,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                allowedRoles: [\n                    \"admin\"\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-2\",\n                                                    children: \"Academic Setup\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100\",\n                                                    children: \"Configure classes, subjects, sessions, and grading system\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 282,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                className: \"w-12 h-12 text-white opacity-80\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                lineNumber: 287,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 286,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 281,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 280,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Classes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: classes.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 295,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 294,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Subjects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: subjects.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-green-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 307,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 306,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Total Students\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-3xl font-bold text-gray-900\",\n                                                            children: classes.reduce((sum, c)=>sum + c.currentStudents, 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-purple-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 319,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 318,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                            children: \"Current Session\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: sessions.find((s)=>s.status === \"current\")?.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-orange-50 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                        className: \"w-8 h-8 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 331,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 330,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 293,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:w-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                className: \"space-y-2\",\n                                                children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveTab(tab.id),\n                                                        className: `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${activeTab === tab.id ? \"bg-primary text-white\" : \"text-gray-700 hover:bg-gray-100\"}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: tab.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: tab.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, tab.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 346,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 345,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-8\",\n                                            children: [\n                                                activeTab === \"classes\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Class Management\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"class\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 378,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Class\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Class Name\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 387,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Level\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 388,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Students\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 389,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Capacity\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 390,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Class Teacher\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 391,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 392,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: classItem.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 398,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.level\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 399,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.currentStudents\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 400,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.capacity\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 401,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: classItem.classTeacherName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"font-medium\",\n                                                                                                    children: classItem.classTeacherName\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 405,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-gray-500\",\n                                                                                                    children: [\n                                                                                                        \"@\",\n                                                                                                        classItem.classTeacherUsername\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 406,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 404,\n                                                                                            columnNumber: 33\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400\",\n                                                                                            children: \"No teacher assigned\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 409,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 402,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 415,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 414,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>handleDelete(\"class\", classItem.id),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 421,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 417,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 413,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 412,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, classItem.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 397,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"subjects\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Subject Management\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"subject\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 442,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Subject\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Subject Name\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 451,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Code\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 452,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Category\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 453,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Credit Units\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 454,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Teacher\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 455,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 456,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: subject.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 462,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.code\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 463,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.category\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 464,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.creditUnits\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 465,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: subject.teacher\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 466,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 470,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 469,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>handleDelete(\"subject\", subject.id),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 476,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 472,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 468,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 467,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, subject.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 461,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"sessions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Academic Sessions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>openAddModal(\"session\"),\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Session\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                                            children: sessions.map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border border-gray-200 rounded-lg p-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between items-start mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                                    children: session.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 506,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: `px-3 py-1 text-xs font-semibold rounded-full ${session.status === \"current\" ? \"bg-green-100 text-green-800\" : session.status === \"upcoming\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"}`,\n                                                                                    children: session.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: \"Start:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 516,\n                                                                                            columnNumber: 30\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        new Date(session.startDate).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: \"End:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 517,\n                                                                                            columnNumber: 30\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        new Date(session.endDate).toLocaleDateString()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 517,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 515,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 mt-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 521,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 520,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleDelete(\"session\", session.id),\n                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 527,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 523,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 519,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, session.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this),\n                                                activeTab === \"grading\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: \"Grading System Configuration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setNewGrade({});\n                                                                        setEditingGradeIndex(-1);\n                                                                        setShowGradeModal(true);\n                                                                    },\n                                                                    className: \"flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlusIcon, {\n                                                                            className: \"w-5 h-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Add Grade\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                            children: \"Grading Type\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: gradingSystem.type,\n                                                                            onChange: (e)=>setGradingSystem({\n                                                                                    ...gradingSystem,\n                                                                                    type: e.target.value\n                                                                                }),\n                                                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"percentage\",\n                                                                                    children: \"Percentage\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 562,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"points\",\n                                                                                    children: \"Points\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 563,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"letter\",\n                                                                                    children: \"Letter Grade\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 564,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                            children: \"Pass Mark (%)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            min: \"0\",\n                                                                            max: \"100\",\n                                                                            value: gradingSystem.passMarkPercentage,\n                                                                            onChange: (e)=>setGradingSystem({\n                                                                                    ...gradingSystem,\n                                                                                    passMarkPercentage: parseInt(e.target.value)\n                                                                                }),\n                                                                            className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Grade\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 584,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Min Score\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 585,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Max Score\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 586,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Points\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 587,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Remark\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 588,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                    lineNumber: 589,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                                        children: gradingSystem.grades?.map((grade, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                className: \"hover:bg-gray-50\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap font-medium text-gray-900\",\n                                                                                        children: grade.grade\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 595,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.minScore\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 596,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.maxScore\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 597,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.points\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 598,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                                                        children: grade.remark\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 599,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>editGrade(index),\n                                                                                                    className: \"text-blue-600 hover:text-blue-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 606,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 602,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>deleteGrade(index),\n                                                                                                    className: \"text-red-600 hover:text-red-900 p-1 rounded\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TrashIcon, {\n                                                                                                        className: \"w-4 h-4\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                        lineNumber: 612,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                                    lineNumber: 608,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                            lineNumber: 601,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                        lineNumber: 600,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                                lineNumber: 594,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-end pt-6 border-t border-gray-200 mt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSave,\n                                                        disabled: isSaving,\n                                                        className: `flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors ${isSaving ? \"bg-gray-400 cursor-not-allowed\" : \"bg-primary hover:bg-primary/90\"} text-white`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SaveIcon, {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: isSaving ? \"Saving...\" : \"Save Changes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 368,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                        lineNumber: 367,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                lineNumber: 343,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                        lineNumber: 278,\n                        columnNumber: 7\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: [\n                                \"Add New \",\n                                modalType\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 650,\n                            columnNumber: 15\n                        }, this),\n                        modalType === \"class\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Class Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 655,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            placeholder: \"e.g., SS1A\",\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 656,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 654,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 666,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.level || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    level: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS1\",\n                                                    children: \"JSS 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS2\",\n                                                    children: \"JSS 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"JSS3\",\n                                                    children: \"JSS 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS1\",\n                                                    children: \"SS 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS2\",\n                                                    children: \"SS 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SS3\",\n                                                    children: \"SS 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 667,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 665,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Class Teacher\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 683,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: teacherSearch,\n                                                    onChange: (e)=>handleTeacherSearch(e.target.value),\n                                                    onFocus: ()=>{\n                                                        setShowTeacherDropdown(true);\n                                                        setFilteredTeachers(teachers);\n                                                    },\n                                                    placeholder: \"Type teacher name or username...\",\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 23\n                                                }, this),\n                                                showTeacherDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto\",\n                                                    children: searchingTeachers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"inline-flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                \"Searching...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 29\n                                                    }, this) : filteredTeachers.length > 0 ? filteredTeachers.map((teacher)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onClick: ()=>{\n                                                                setSelectedTeacher(teacher);\n                                                                setTeacherSearch(\"\");\n                                                                setShowTeacherDropdown(false);\n                                                            },\n                                                            className: \"p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: teacher.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 716,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"@\",\n                                                                        teacher.username\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                                    lineNumber: 717,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, teacher.uid, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 31\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 text-center text-gray-500\",\n                                                        children: teacherSearch ? \"No teachers found\" : \"Start typing to search...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 684,\n                                            columnNumber: 21\n                                        }, this),\n                                        selectedTeacher && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-3 bg-blue-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-blue-900\",\n                                                    children: [\n                                                        \"Selected: \",\n                                                        selectedTeacher.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-700\",\n                                                    children: [\n                                                        \"@\",\n                                                        selectedTeacher.username\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 729,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 682,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 653,\n                            columnNumber: 17\n                        }, this),\n                        modalType === \"subject\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Subject Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 741,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 742,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 740,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Subject Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 751,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.code || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    code: e.target.value.toUpperCase()\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 752,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 750,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 761,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.category || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    category: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Core\",\n                                                    children: \"Core\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Science\",\n                                                    children: \"Science\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Arts\",\n                                                    children: \"Arts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Social Science\",\n                                                    children: \"Social Science\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Vocational\",\n                                                    children: \"Vocational\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 762,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 760,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 739,\n                            columnNumber: 17\n                        }, this),\n                        modalType === \"session\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 782,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: newItem.name || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    name: e.target.value\n                                                }),\n                                            placeholder: \"e.g., 2024/2025\",\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 783,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 781,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Start Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 793,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            required: true,\n                                            value: newItem.startDate || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    startDate: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 794,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 792,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"End Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 803,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            required: true,\n                                            value: newItem.endDate || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    endDate: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 804,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 802,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 813,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newItem.status || \"\",\n                                            onChange: (e)=>setNewItem({\n                                                    ...newItem,\n                                                    status: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"upcoming\",\n                                                    children: \"Upcoming\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"current\",\n                                                    children: \"Current\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 821,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"completed\",\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 814,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 812,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 780,\n                            columnNumber: 17\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddItem,\n                                    disabled: isSaving,\n                                    className: \"flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50\",\n                                    children: isSaving ? \"Creating...\" : `Create ${modalType}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 829,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddModal(false),\n                                    className: \"flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 836,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 828,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 649,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 648,\n                columnNumber: 7\n            }, this),\n            showGradeModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-4\",\n                            children: editingGradeIndex >= 0 ? \"Edit Grade\" : \"Add New Grade\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 851,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Grade Letter\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 854,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            maxLength: \"2\",\n                                            value: newGrade.grade || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    grade: e.target.value.toUpperCase()\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 855,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 853,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Min Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 865,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    value: newGrade.minScore || \"\",\n                                                    onChange: (e)=>setNewGrade({\n                                                            ...newGrade,\n                                                            minScore: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 864,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Max Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 876,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"0\",\n                                                    max: \"100\",\n                                                    value: newGrade.maxScore || \"\",\n                                                    onChange: (e)=>setNewGrade({\n                                                            ...newGrade,\n                                                            maxScore: parseInt(e.target.value)\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 875,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 863,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Points\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 888,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"0\",\n                                            max: \"10\",\n                                            value: newGrade.points || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    points: parseInt(e.target.value)\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 889,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 887,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Remark\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 899,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: newGrade.remark || \"\",\n                                            onChange: (e)=>setNewGrade({\n                                                    ...newGrade,\n                                                    remark: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                            lineNumber: 900,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 898,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 852,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: editingGradeIndex >= 0 ? updateGrade : addGrade,\n                                    className: \"flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                                    children: editingGradeIndex >= 0 ? \"Update Grade\" : \"Add Grade\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 909,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowGradeModal(false);\n                                        setNewGrade({});\n                                        setEditingGradeIndex(-1);\n                                    },\n                                    className: \"flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                                    lineNumber: 915,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                            lineNumber: 908,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                    lineNumber: 850,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin\\\\academic.js\",\n                lineNumber: 849,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard/admin/academic.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "firebase/app":
/*!*******************************!*\
  !*** external "firebase/app" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/app");;

/***/ }),

/***/ "firebase/auth":
/*!********************************!*\
  !*** external "firebase/auth" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/auth");;

/***/ }),

/***/ "firebase/firestore":
/*!*************************************!*\
  !*** external "firebase/firestore" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/firestore");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin%2Facademic&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin%5Cacademic.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();