import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../../components/DashboardLayout'
import ProtectedRoute from '../../../components/ProtectedRoute'
import { 
  getAllClasses, 
  getAllSubjects, 
  getAllSessions, 
  createClass, 
  createSubject, 
  createSession,
  deleteClass,
  deleteSubject,
  deleteSession
} from '../../../lib/academicService'
import { getUsersByRole } from '../../../lib/authService'

// Professional SVG Icons
const AcademicCapIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
  </svg>
)

const PlusIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
  </svg>
)

const EditIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
  </svg>
)

const TrashIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
  </svg>
)

const SaveIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3-3-3m3-3v12" />
  </svg>
)

export default function AdminAcademic() {
  const [activeTab, setActiveTab] = useState('classes')
  const [classes, setClasses] = useState([])
  const [subjects, setSubjects] = useState([])
  const [sessions, setSessions] = useState([])
  const [gradingSystem, setGradingSystem] = useState({
    type: 'percentage',
    passMarkPercentage: 40,
    grades: [
      { grade: 'A', minScore: 80, maxScore: 100, points: 5, remark: 'Excellent' },
      { grade: 'B', minScore: 70, maxScore: 79, points: 4, remark: 'Very Good' },
      { grade: 'C', minScore: 60, maxScore: 69, points: 3, remark: 'Good' },
      { grade: 'D', minScore: 50, maxScore: 59, points: 2, remark: 'Pass' },
      { grade: 'E', minScore: 40, maxScore: 49, points: 1, remark: 'Poor' },
      { grade: 'F', minScore: 0, maxScore: 39, points: 0, remark: 'Fail' }
    ]
  })
  const [showGradeModal, setShowGradeModal] = useState(false)
  const [newGrade, setNewGrade] = useState({})
  const [editingGradeIndex, setEditingGradeIndex] = useState(-1)
  const [showAddModal, setShowAddModal] = useState(false)
  const [modalType, setModalType] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [teachers, setTeachers] = useState([])
  const [teacherSearch, setTeacherSearch] = useState('')
  const [selectedTeacher, setSelectedTeacher] = useState(null)
  const [newItem, setNewItem] = useState({})
  const [filteredTeachers, setFilteredTeachers] = useState([])
  const [showTeacherDropdown, setShowTeacherDropdown] = useState(false)
  const [searchingTeachers, setSearchingTeachers] = useState(false)
  const router = useRouter()

  useEffect(() => {
    loadAcademicData()
  }, [])

  const loadAcademicData = async () => {
    try {
      console.log('Loading academic data...')
      const [classesData, subjectsData, sessionsData, teachersData] = await Promise.all([
        getAllClasses(),
        getAllSubjects(), 
        getAllSessions(),
        getAllUsers()
      ])
      
      // Debug: Log all users first
      console.log('DEBUG - All users loaded:', teachersData.length, teachersData)

      // Filter teachers from all users
      const teachersOnly = teachersData.filter(user => user.role === 'teacher')
      console.log('DEBUG - Teachers found:', teachersOnly.length, teachersOnly)

      // Debug: Check what roles exist
      const allRoles = [...new Set(teachersData.map(user => user.role))]
      console.log('DEBUG - All roles in database:', allRoles)
      
      setClasses(classesData)
      setSubjects(subjectsData)
      setSessions(sessionsData)
      setTeachers(teachersOnly)
      setFilteredTeachers(teachersOnly)
      
      // Keep existing grading system
    } catch (error) {
      console.error('Error loading academic data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // Mock API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 1500))
      alert('Academic setup saved successfully!')
    } catch (error) {
      console.error('Error saving academic setup:', error)
      alert('Error saving academic setup. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleDelete = async (type, id) => {
    if (confirm(`Are you sure you want to delete this ${type}?`)) {
      try {
        switch (type) {
          case 'class':
            await deleteClass(id)
            setClasses(classes.filter(c => c.id !== id))
            break
          case 'subject':
            await deleteSubject(id)
            setSubjects(subjects.filter(s => s.id !== id))
            break
          case 'session':
            await deleteSession(id)
            setSessions(sessions.filter(s => s.id !== id))
            break
        }
        alert(`${type} deleted successfully!`)
      } catch (error) {
        console.error(`Error deleting ${type}:`, error)
        alert(`Error deleting ${type}. Please try again.`)
      }
    }
  }

  const openAddModal = (type) => {
    setModalType(type)
    setNewItem({})
    setSelectedTeacher(null)
    setTeacherSearch('')
    setFilteredTeachers(teachers) // Initialize with all teachers
    setShowTeacherDropdown(false)
    setSearchingTeachers(false)
    setShowAddModal(true)
  }

  const handleAddItem = async () => {
    try {
      setIsSaving(true)
      let result
      
      switch (modalType) {
        case 'class':
          const classData = {
            ...newItem,
            classTeacherId: selectedTeacher?.uid,
            classTeacherName: selectedTeacher?.name,
            classTeacherUsername: selectedTeacher?.username,
            currentStudents: 0,
            capacity: 0 // Will be calculated based on enrolled students
          }
          result = await createClass(classData)
          setClasses([...classes, result])
          break
          
        case 'subject':
          result = await createSubject(newItem)
          setSubjects([...subjects, result])
          break
          
        case 'session':
          result = await createSession(newItem)
          setSessions([...sessions, result])
          break
      }
      
      setShowAddModal(false)
      alert(`${modalType} created successfully!`)
    } catch (error) {
      console.error(`Error creating ${modalType}:`, error)
      alert(`Error creating ${modalType}. Please try again.`)
    } finally {
      setIsSaving(false)
    }
  }

  const handleTeacherSearch = (searchTerm) => {
    setTeacherSearch(searchTerm)
    setShowTeacherDropdown(true)

    console.log('DEBUG - Search term:', searchTerm)
    console.log('DEBUG - Available teachers for search:', teachers.length, teachers)

    if (searchTerm.trim().length > 0) {
      setSearchingTeachers(true)
      // Small delay to show spinner as you intended
      setTimeout(() => {
        const filtered = teachers.filter(teacher => {
          const nameMatch = teacher.name?.toLowerCase().includes(searchTerm.toLowerCase())
          const usernameMatch = teacher.username?.toLowerCase().includes(searchTerm.toLowerCase())
          const emailMatch = teacher.email?.toLowerCase().includes(searchTerm.toLowerCase())

          console.log('DEBUG - Teacher:', teacher.name, 'Name match:', nameMatch, 'Username match:', usernameMatch, 'Email match:', emailMatch)

          return nameMatch || usernameMatch || emailMatch
        })

        console.log('DEBUG - Filtered results:', filtered.length, filtered)
        setFilteredTeachers(filtered)
        setSearchingTeachers(false)
      }, 300)
    } else {
      // Show all teachers when search is empty
      setFilteredTeachers(teachers)
      setSearchingTeachers(false)
    }
  }

  const addGrade = () => {
    if (newGrade.grade && newGrade.minScore !== undefined && newGrade.maxScore !== undefined) {
      const updatedGrades = [...gradingSystem.grades, { ...newGrade, points: gradingSystem.grades.length }]
      setGradingSystem({ ...gradingSystem, grades: updatedGrades })
      setNewGrade({})
      setShowGradeModal(false)
    }
  }

  const editGrade = (index) => {
    setNewGrade(gradingSystem.grades[index])
    setEditingGradeIndex(index)
    setShowGradeModal(true)
  }

  const updateGrade = () => {
    const updatedGrades = [...gradingSystem.grades]
    updatedGrades[editingGradeIndex] = newGrade
    setGradingSystem({ ...gradingSystem, grades: updatedGrades })
    setNewGrade({})
    setEditingGradeIndex(-1)
    setShowGradeModal(false)
  }

  const deleteGrade = (index) => {
    if (confirm('Are you sure you want to delete this grade?')) {
      const updatedGrades = gradingSystem.grades.filter((_, i) => i !== index)
      setGradingSystem({ ...gradingSystem, grades: updatedGrades })
    }
  }

  const tabs = [
    { id: 'classes', name: 'Classes', icon: '🏫' },
    { id: 'subjects', name: 'Subjects', icon: '📚' },
    { id: 'sessions', name: 'Sessions', icon: '📅' },
    { id: 'grading', name: 'Grading System', icon: '📊' }
  ]

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <>
      <ProtectedRoute allowedRoles={['admin']}>
        <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Academic Setup</h1>
              <p className="text-blue-100">Configure classes, subjects, sessions, and grading system</p>
            </div>
            <div className="hidden md:block">
              <AcademicCapIcon className="w-12 h-12 text-white opacity-80" />
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Classes</p>
                <p className="text-3xl font-bold text-gray-900">{classes.length}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <AcademicCapIcon className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Subjects</p>
                <p className="text-3xl font-bold text-gray-900">{subjects.length}</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <AcademicCapIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Students</p>
                <p className="text-3xl font-bold text-gray-900">{classes.reduce((sum, c) => sum + c.currentStudents, 0)}</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <AcademicCapIcon className="w-8 h-8 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Current Session</p>
                <p className="text-lg font-bold text-gray-900">{sessions.find(s => s.status === 'current')?.name}</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <AcademicCapIcon className="w-8 h-8 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Tabs */}
          <div className="lg:w-64">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <span className="text-lg">{tab.icon}</span>
                    <span className="font-medium">{tab.name}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
              {/* Classes Tab */}
              {activeTab === 'classes' && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-semibold text-gray-900">Class Management</h3>
                    <button
                      onClick={() => openAddModal('class')}
                      className="flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      <PlusIcon className="w-5 h-5" />
                      <span>Add Class</span>
                    </button>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class Name</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Capacity</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class Teacher</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {classes.map((classItem) => (
                          <tr key={classItem.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">{classItem.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{classItem.level}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{classItem.currentStudents}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{classItem.capacity}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {classItem.classTeacherName ? (
                                <div>
                                  <div className="font-medium">{classItem.classTeacherName}</div>
                                  <div className="text-xs text-gray-500">@{classItem.classTeacherUsername}</div>
                                </div>
                              ) : (
                                <span className="text-gray-400">No teacher assigned</span>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                <button className="text-blue-600 hover:text-blue-900 p-1 rounded">
                                  <EditIcon className="w-4 h-4" />
                                </button>
                                <button 
                                  onClick={() => handleDelete('class', classItem.id)}
                                  className="text-red-600 hover:text-red-900 p-1 rounded"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Subjects Tab */}
              {activeTab === 'subjects' && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-semibold text-gray-900">Subject Management</h3>
                    <button
                      onClick={() => openAddModal('subject')}
                      className="flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      <PlusIcon className="w-5 h-5" />
                      <span>Add Subject</span>
                    </button>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject Name</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credit Units</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {subjects.map((subject) => (
                          <tr key={subject.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">{subject.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subject.code}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subject.category}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subject.creditUnits}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subject.teacher}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                <button className="text-blue-600 hover:text-blue-900 p-1 rounded">
                                  <EditIcon className="w-4 h-4" />
                                </button>
                                <button 
                                  onClick={() => handleDelete('subject', subject.id)}
                                  className="text-red-600 hover:text-red-900 p-1 rounded"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Sessions Tab */}
              {activeTab === 'sessions' && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-semibold text-gray-900">Academic Sessions</h3>
                    <button
                      onClick={() => openAddModal('session')}
                      className="flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      <PlusIcon className="w-5 h-5" />
                      <span>Add Session</span>
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {sessions.map((session) => (
                      <div key={session.id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex justify-between items-start mb-4">
                          <h4 className="text-lg font-semibold text-gray-900">{session.name}</h4>
                          <span className={`px-3 py-1 text-xs font-semibold rounded-full ${
                            session.status === 'current' ? 'bg-green-100 text-green-800' :
                            session.status === 'upcoming' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {session.status}
                          </span>
                        </div>
                        <div className="space-y-2 text-sm text-gray-600">
                          <p><span className="font-medium">Start:</span> {new Date(session.startDate).toLocaleDateString()}</p>
                          <p><span className="font-medium">End:</span> {new Date(session.endDate).toLocaleDateString()}</p>
                        </div>
                        <div className="flex items-center space-x-2 mt-4">
                          <button className="text-blue-600 hover:text-blue-900 p-1 rounded">
                            <EditIcon className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => handleDelete('session', session.id)}
                            className="text-red-600 hover:text-red-900 p-1 rounded"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Grading System Tab */}
              {activeTab === 'grading' && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-semibold text-gray-900">Grading System Configuration</h3>
                    <button
                      onClick={() => {
                        setNewGrade({})
                        setEditingGradeIndex(-1)
                        setShowGradeModal(true)
                      }}
                      className="flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      <PlusIcon className="w-5 h-5" />
                      <span>Add Grade</span>
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Grading Type</label>
                      <select
                        value={gradingSystem.type}
                        onChange={(e) => setGradingSystem({...gradingSystem, type: e.target.value})}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                      >
                        <option value="percentage">Percentage</option>
                        <option value="points">Points</option>
                        <option value="letter">Letter Grade</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Pass Mark (%)</label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={gradingSystem.passMarkPercentage}
                        onChange={(e) => setGradingSystem({...gradingSystem, passMarkPercentage: parseInt(e.target.value)})}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                      />
                    </div>
                  </div>

                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Min Score</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max Score</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remark</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {gradingSystem.grades?.map((grade, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">{grade.grade}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.minScore}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.maxScore}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.points}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.remark}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                <button 
                                  onClick={() => editGrade(index)}
                                  className="text-blue-600 hover:text-blue-900 p-1 rounded"
                                >
                                  <EditIcon className="w-4 h-4" />
                                </button>
                                <button 
                                  onClick={() => deleteGrade(index)}
                                  className="text-red-600 hover:text-red-900 p-1 rounded"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Save Button */}
              <div className="flex justify-end pt-6 border-t border-gray-200 mt-8">
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors ${
                    isSaving 
                      ? 'bg-gray-400 cursor-not-allowed' 
                      : 'bg-primary hover:bg-primary/90'
                  } text-white`}
                >
                  <SaveIcon className="w-5 h-5" />
                  <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
        </DashboardLayout>
      </ProtectedRoute>
      
      {/* Add Item Modal */}
    {showAddModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style={{position: 'fixed', top: 0, left: 0, right: 0, bottom: 0}}>
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
              <h2 className="text-xl font-bold mb-4">Add New {modalType}</h2>
              
              {modalType === 'class' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Class Name</label>
                    <input
                      type="text"
                      required
                      value={newItem.name || ''}
                      onChange={(e) => setNewItem({...newItem, name: e.target.value})}
                      placeholder="e.g., SS1A"
                      className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Level</label>
                    <select
                      value={newItem.level || ''}
                      onChange={(e) => setNewItem({...newItem, level: e.target.value})}
                      className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    >
                      <option value="">Select Level</option>
                      <option value="JSS1">JSS 1</option>
                      <option value="JSS2">JSS 2</option>
                      <option value="JSS3">JSS 3</option>
                      <option value="SS1">SS 1</option>
                      <option value="SS2">SS 2</option>
                      <option value="SS3">SS 3</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Class Teacher</label>
                    <div className="relative">
                      <input
                        type="text"
                        value={teacherSearch}
                        onChange={(e) => handleTeacherSearch(e.target.value)}
                        onFocus={() => {
                          setShowTeacherDropdown(true)
                          if (teacherSearch.trim() === '') {
                            setFilteredTeachers(teachers)
                          }
                        }}
                        onBlur={() => {
                          // Delay hiding dropdown to allow clicking on options
                          setTimeout(() => {
                            setShowTeacherDropdown(false)
                          }, 150)
                        }}
                        placeholder="Type teacher name or username..."
                        className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                      />
                      {showTeacherDropdown && (
                        <div
                          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto"
                          onMouseDown={(e) => e.preventDefault()} // Prevent input blur when clicking dropdown
                        >
                          {searchingTeachers ? (
                            <div className="p-3 text-center">
                              <div className="inline-flex items-center">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                                Searching...
                              </div>
                            </div>
                          ) : filteredTeachers.length > 0 ? (
                            filteredTeachers.map((teacher) => (
                              <div
                                key={teacher.uid}
                                onClick={() => {
                                  setSelectedTeacher(teacher)
                                  setTeacherSearch(teacher.name) // Show selected teacher name in input
                                  setShowTeacherDropdown(false)
                                }}
                                className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                              >
                                <div className="font-medium text-gray-900">{teacher.name}</div>
                                <div className="text-sm text-gray-500">@{teacher.username}</div>
                                {teacher.email && (
                                  <div className="text-xs text-gray-400">{teacher.email}</div>
                                )}
                              </div>
                            ))
                          ) : (
                            <div className="p-3 text-center text-gray-500">
                              {teacherSearch ? 'No teachers found matching your search' : 'Start typing to search teachers...'}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    {selectedTeacher && (
                      <div className="mt-2 p-3 bg-blue-50 rounded-lg flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium text-blue-900">Selected: {selectedTeacher.name}</div>
                          <div className="text-xs text-blue-700">@{selectedTeacher.username}</div>
                        </div>
                        <button
                          type="button"
                          onClick={() => {
                            setSelectedTeacher(null)
                            setTeacherSearch('')
                            setFilteredTeachers(teachers)
                          }}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          Clear
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {modalType === 'subject' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Subject Name</label>
                    <input
                      type="text"
                      required
                      value={newItem.name || ''}
                      onChange={(e) => setNewItem({...newItem, name: e.target.value})}
                      className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Subject Code</label>
                    <input
                      type="text"
                      required
                      value={newItem.code || ''}
                      onChange={(e) => setNewItem({...newItem, code: e.target.value.toUpperCase()})}
                      className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select
                      value={newItem.category || ''}
                      onChange={(e) => setNewItem({...newItem, category: e.target.value})}
                      className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    >
                      <option value="">Select Category</option>
                      <option value="Core">Core</option>
                      <option value="Science">Science</option>
                      <option value="Arts">Arts</option>
                      <option value="Social Science">Social Science</option>
                      <option value="Vocational">Vocational</option>
                    </select>
                  </div>

                </div>
              )}
              
              {modalType === 'session' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Session Name</label>
                    <input
                      type="text"
                      required
                      value={newItem.name || ''}
                      onChange={(e) => setNewItem({...newItem, name: e.target.value})}
                      placeholder="e.g., 2024/2025"
                      className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input
                      type="date"
                      required
                      value={newItem.startDate || ''}
                      onChange={(e) => setNewItem({...newItem, startDate: e.target.value})}
                      className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input
                      type="date"
                      required
                      value={newItem.endDate || ''}
                      onChange={(e) => setNewItem({...newItem, endDate: e.target.value})}
                      className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                      value={newItem.status || ''}
                      onChange={(e) => setNewItem({...newItem, status: e.target.value})}
                      className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    >
                      <option value="">Select Status</option>
                      <option value="upcoming">Upcoming</option>
                      <option value="current">Current</option>
                      <option value="completed">Completed</option>
                    </select>
                  </div>
                </div>
              )}
              
              <div className="flex gap-2 pt-4">
                <button
                  onClick={handleAddItem}
                  disabled={isSaving}
                  className="flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
                >
                  {isSaving ? 'Creating...' : `Create ${modalType}`}
                </button>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
    )}
    
    {/* Grade Modal */}
    {showGradeModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style={{position: 'fixed', top: 0, left: 0, right: 0, bottom: 0}}>
        <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
          <h2 className="text-xl font-bold mb-4">{editingGradeIndex >= 0 ? 'Edit Grade' : 'Add New Grade'}</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Grade Letter</label>
              <input
                type="text"
                maxLength="2"
                value={newGrade.grade || ''}
                onChange={(e) => setNewGrade({...newGrade, grade: e.target.value.toUpperCase()})}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Min Score</label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={newGrade.minScore || ''}
                  onChange={(e) => setNewGrade({...newGrade, minScore: parseInt(e.target.value)})}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Max Score</label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={newGrade.maxScore || ''}
                  onChange={(e) => setNewGrade({...newGrade, maxScore: parseInt(e.target.value)})}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Points</label>
              <input
                type="number"
                min="0"
                max="10"
                value={newGrade.points || ''}
                onChange={(e) => setNewGrade({...newGrade, points: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Remark</label>
              <input
                type="text"
                value={newGrade.remark || ''}
                onChange={(e) => setNewGrade({...newGrade, remark: e.target.value})}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
              />
            </div>
          </div>
          <div className="flex gap-2 pt-4">
            <button
              onClick={editingGradeIndex >= 0 ? updateGrade : addGrade}
              className="flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary/90 transition-colors"
            >
              {editingGradeIndex >= 0 ? 'Update Grade' : 'Add Grade'}
            </button>
            <button
              onClick={() => {
                setShowGradeModal(false)
                setNewGrade({})
                setEditingGradeIndex(-1)
              }}
              className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    )}
    </>
  )
}
