import Link from 'next/link'
import Image from 'next/image'
import Layout from '../components/Layout'

export default function Forbidden() {
  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center py-12 px-4">
        <div className="max-w-md w-full text-center">
          <div className="mb-8">
            <Link href="/" className="inline-flex items-center space-x-3 group mb-8">
              <Image 
                src="/GHC.jpg" 
                alt="Great Heritage College" 
                width={60}
                height={60}
                className="rounded-full" 
              />
              <span className="text-2xl font-bold text-primary">Great Heritage College</span>
            </Link>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-8 border border-red-100">
            <div className="text-6xl font-bold text-red-500 mb-4">403</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Forbidden</h1>
            <p className="text-gray-600 mb-8">
              You don't have permission to access this resource. Please contact your administrator if you believe this is an error.
            </p>
            
            <div className="space-y-4">
              <Link 
                href="/login" 
                className="block w-full btn btn-primary"
              >
                Back to Login
              </Link>
              <Link 
                href="/" 
                className="block w-full btn text-primary"
              >
                Go to Home
              </Link>
            </div>
          </div>

          <div className="mt-8 text-sm text-gray-500">
            <p>Need help? Contact us at <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a></p>
          </div>
        </div>
      </div>
    </Layout>
  )
}